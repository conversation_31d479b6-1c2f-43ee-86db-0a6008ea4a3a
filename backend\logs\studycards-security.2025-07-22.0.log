2025-07-22 21:20:57.960 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT secret validation successful. Secret length: 87 bytes
2025-07-22 21:20:57.976 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT configuration initialized successfully
2025-07-22 21:20:57.978 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT expiration: 14400000 ms (4 hours)
2025-07-22 21:20:57.978 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT refresh expiration: 604800000 ms (168 hours)
2025-07-22 21:20:57.978 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT configuration: JwtProperties{expiration=14400000, refreshExpiration=604800000, refreshCookieName='studycards-refresh-token', issuer='StudyCards', audience='StudyCards-Users', maxSessionDurationDays=365, refreshWindowDays=7, requireHttps=false, includeDetailedErrors=true, secretLength=116}
2025-07-22 21:29:24.913 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT secret validation successful. Secret length: 87 bytes
2025-07-22 21:29:24.920 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT configuration initialized successfully
2025-07-22 21:29:24.920 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT expiration: 14400000 ms (4 hours)
2025-07-22 21:29:24.920 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT refresh expiration: 604800000 ms (168 hours)
2025-07-22 21:29:24.920 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT configuration: JwtProperties{expiration=14400000, refreshExpiration=604800000, refreshCookieName='studycards-refresh-token', issuer='StudyCards', audience='StudyCards-Users', maxSessionDurationDays=365, refreshWindowDays=7, requireHttps=false, includeDetailedErrors=true, secretLength=116}
2025-07-22 21:40:29.304 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT secret validation successful. Secret length: 87 bytes
2025-07-22 21:40:29.309 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT configuration initialized successfully
2025-07-22 21:40:29.309 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT expiration: 14400000 ms (4 hours)
2025-07-22 21:40:29.310 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT refresh expiration: 604800000 ms (168 hours)
2025-07-22 21:40:29.310 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT configuration: JwtProperties{expiration=14400000, refreshExpiration=604800000, refreshCookieName='studycards-refresh-token', issuer='StudyCards', audience='StudyCards-Users', maxSessionDurationDays=365, refreshWindowDays=7, requireHttps=false, includeDetailedErrors=true, secretLength=116}
2025-07-22 23:02:34.164 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT secret validation successful. Secret length: 87 bytes
2025-07-22 23:02:34.169 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT configuration initialized successfully
2025-07-22 23:02:34.169 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT expiration: 14400000 ms (4 hours)
2025-07-22 23:02:34.169 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT refresh expiration: 604800000 ms (168 hours)
2025-07-22 23:02:34.169 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT configuration: JwtProperties{expiration=14400000, refreshExpiration=604800000, refreshCookieName='studycards-refresh-token', issuer='StudyCards', audience='StudyCards-Users', maxSessionDurationDays=365, refreshWindowDays=7, requireHttps=false, includeDetailedErrors=true, secretLength=116}
2025-07-22 23:07:56.356 [http-nio-8082-exec-2] INFO  c.s.security.CustomOAuth2UserService - 
                [] [] [] [] Google OAuth2 user attributes validated successfully for email: <EMAIL>
2025-07-22 23:07:56.362 [http-nio-8082-exec-2] INFO  c.s.s.OAuth2AuthenticationSuccessHandler - 
                [] [] [] [] OAuth2 authentication success handler called
2025-07-22 23:07:56.362 [http-nio-8082-exec-2] INFO  c.s.s.OAuth2AuthenticationSuccessHandler - 
                [] [] [] [] Request URI: /login/oauth2/code/google
2025-07-22 23:07:56.362 [http-nio-8082-exec-2] INFO  c.s.s.OAuth2AuthenticationSuccessHandler - 
                [] [] [] [] Query string: state=01bDWfdZuqhR1ZJmq_Y195RSFV1qxK0alnCk4CNbw_Y%3D&code=4%2F0AVMBsJiE-SCo7IDaAS2ZBUprDDKTjj11nOr1l7ognUpFHKCe9XCucLb7t3W9u3sumVzL8A&scope=email+profile+https%3A%2F%2Fwww.googleapis.com%2Fauth%2Fuserinfo.email+https%3A%2F%2Fwww.googleapis.com%2Fauth%2Fuserinfo.profile+openid&authuser=1&prompt=none
2025-07-22 23:07:56.363 [http-nio-8082-exec-2] INFO  c.s.s.OAuth2AuthenticationSuccessHandler - 
                [] [] [] [] OAuth2 authentication success from Code
2025-07-22 23:07:56.452 [http-nio-8082-exec-2] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT token generated successfully for email: <EMAIL> (ID: 7a9385c4-ebfe-4b68-aa75-2bea461a2b54)
2025-07-22 23:07:56.573 [http-nio-8082-exec-2] INFO  c.s.s.OAuth2AuthenticationSuccessHandler - 
                [] [] [] [] OAuth2 cookie configuration - requireHttps: false, isSecure: false, useSecureCookies: false
2025-07-22 23:07:56.573 [http-nio-8082-exec-2] INFO  c.s.s.OAuth2AuthenticationSuccessHandler - 
                [] [] [] [] OAuth2 access token cookie set - secure: false, httpOnly: false, path: /, maxAge: 300
2025-07-22 23:07:56.573 [http-nio-8082-exec-2] INFO  c.s.s.OAuth2AuthenticationSuccessHandler - 
                [] [] [] [] OAuth2 refresh token cookie set - secure: false, httpOnly: false, path: /, maxAge: 300
2025-07-22 23:24:39.622 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT secret validation successful. Secret length: 87 bytes
2025-07-22 23:24:39.638 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT configuration initialized successfully
2025-07-22 23:24:39.638 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT expiration: 14400000 ms (4 hours)
2025-07-22 23:24:39.638 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT refresh expiration: 604800000 ms (168 hours)
2025-07-22 23:24:39.638 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT configuration: JwtProperties{expiration=14400000, refreshExpiration=604800000, refreshCookieName='studycards-refresh-token', issuer='StudyCards', audience='StudyCards-Users', maxSessionDurationDays=365, refreshWindowDays=7, requireHttps=false, includeDetailedErrors=true, secretLength=116}
2025-07-22 23:38:34.693 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT secret validation successful. Secret length: 87 bytes
2025-07-22 23:38:34.701 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT configuration initialized successfully
2025-07-22 23:38:34.701 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT expiration: 14400000 ms (4 hours)
2025-07-22 23:38:34.701 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT refresh expiration: 604800000 ms (168 hours)
2025-07-22 23:38:34.701 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT configuration: JwtProperties{expiration=14400000, refreshExpiration=604800000, refreshCookieName='studycards-refresh-token', issuer='StudyCards', audience='StudyCards-Users', maxSessionDurationDays=365, refreshWindowDays=7, requireHttps=false, includeDetailedErrors=true, secretLength=116}
