// src/pages/SpacedRepetitionStudy.jsx - Enhanced Version with Offline Support
import React, { useState, useEffect, useCallback } from "react";
import { useQuery, useMutation, useQueryClient } from "react-query";
import { usePara<PERSON>, Link, useNavigate, useLocation } from "react-router-dom";
import { toast } from "react-toastify";
import { motion, AnimatePresence } from "framer-motion";
import {
  CalendarIcon,
  CheckCircleIcon,
  XCircleIcon,
  ArrowPathIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  ClockIcon,
  FireIcon,
  LightBulbIcon,
  InformationCircleIcon,
  ArrowLeftIcon,
  AdjustmentsHorizontalIcon,
  CloudIcon,
  ExclamationCircleIcon,
} from "@heroicons/react/24/outline";
import Button from "../components/Button";
import FlashCard from "../components/FlashCard";
import AdvancedFlashCard from "../components/AdvancedFlashCard";
import ProgressBar from "../components/ProgressBar";
import Confetti from "../components/Confetti";
import EnhancedSyncManager from "../components/study/EnhancedSyncManager";
import OfflineStorageService from "../services/OfflineStorageService";
import { studyAPI } from "../api/study";
import { analyticsAPI } from "../api/analytics";
import { decksAPI } from "../api/decks";

const SpacedRepetitionStudy = () => {
  const { deckId } = useParams();
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const location = useLocation();
  const [currentCardIndex, setCurrentCardIndex] = useState(0);
  const [isFlipped, setIsFlipped] = useState(false);
  const [showHint, setShowHint] = useState(false);
  const [cardFlipKey, setCardFlipKey] = useState(0); // For controlling card reset during navigation
  const [sessionStats, setSessionStats] = useState({
    reviewed: 0,
    easy: 0,
    medium: 0,
    hard: 0,
    streak: 0,
  });
  const [sessionComplete, setSessionComplete] = useState(false);
  const [showTutorial, setShowTutorial] = useState(false);
  const [showKeyboardShortcuts, setShowKeyboardShortcuts] = useState(false);
  const [motivationalMessage, setMotivationalMessage] = useState("");
  const [celebrateStreak, setCelebrateStreak] = useState(false);
  const [sessionMetadata, setSessionMetadata] = useState(null);
  const [reviewOptions, setReviewOptions] = useState({
    limit: 20,
    prioritizeDifficult: true,
    includeOverdue: true,
    sortBy: "difficulty",
    includeCollaborative: true
  });
  const [showOptions, setShowOptions] = useState(false);
  const [useEnhancedMode, setUseEnhancedMode] = useState(true);
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [unsyncedCount, setUnsyncedCount] = useState(0);
  const [sessionTime, setSessionTime] = useState(0);
  const [sessionStartTime, setSessionStartTime] = useState(null);
  const [isTimerActive, setIsTimerActive] = useState(false);

  // Check if we're in global review mode
  const isGlobalReview = !deckId;

  // Check if we're using offline data
  const [isOfflineSession, setIsOfflineSession] = useState(false);

  // Fetch review cards using the enhanced API with offline fallback
  const fetchReviewCards = async () => {
    try {
      // First try to fetch from the server if online
      if (isOnline) {
        try {
          if (isGlobalReview) {
            // Fetch cards due for review across all decks
            return await studyAPI.getAllCardsForReview(reviewOptions);
          } else {
            // Fetch cards for a specific deck
            return await studyAPI.getCardsForReview(deckId, reviewOptions);
          }
        } catch (onlineError) {
          console.error("Error fetching cards online, falling back to offline:", onlineError);
          // If server fetch fails, fall back to offline cache
          return await fetchOfflineReviewCards();
        }
      } else {
        // If offline, use cached data
        return await fetchOfflineReviewCards();
      }
    } catch (error) {
      console.error("Error fetching review cards:", error);
      throw error;
    }
  };

  // Fetch cards from offline storage when not connected
  const fetchOfflineReviewCards = async () => {
    try {
      // Get cached cards from offline storage
      const result = await OfflineStorageService.getCachedCards(deckId);

      // Handle the case where the result is an error object
      if (!result || result.error) {
        console.error("Error retrieving cached cards:", result?.error || "No result returned");

        // Show a more helpful error message with retry option
        toast.error(
          <div>
            <p>Failed to retrieve cached cards.</p>
            <button
              className="mt-2 px-2 py-1 bg-primary-600 text-white rounded text-xs"
              onClick={() => {
                // Try to initialize storage again
                OfflineStorageService.initialize()
                  .then(() => fetchOfflineReviewCards())
                  .catch(err => console.error("Failed to reinitialize storage:", err));
              }}
            >
              Retry
            </button>
          </div>,
          { autoClose: false, closeOnClick: false }
        );

        const response = {
          cards: [],
          metadata: {
            sessionId: `offline-${Date.now()}`,
            isOfflineSession: true,
            deckId: deckId || 'global',
            error: result?.error || "Unknown error"
          }
        };
        setIsOfflineSession(true);
        return response;
      }

      // Extract the cards from the result
      const cachedCards = Array.isArray(result) ? result : result.cards || [];

      if (!cachedCards || cachedCards.length === 0) {
        // No cached cards available - provide more helpful guidance
        toast.info(
          <div>
            <p>No cached cards available for offline study.</p>
            {navigator.onLine && (
              <button
                className="mt-2 px-2 py-1 bg-primary-600 text-white rounded text-xs"
                onClick={() => {
                  // Refresh cards from server
                  queryClient.invalidateQueries(["reviewCards", deckId || "global", reviewOptions]);
                }}
              >
                Download Cards Now
              </button>
            )}
          </div>,
          { autoClose: 8000 }
        );

        const response = {
          cards: [],
          metadata: {
            sessionId: `offline-${Date.now()}`,
            isOfflineSession: true,
            deckId: deckId || 'global'
          }
        };
        setIsOfflineSession(true);
        return response;
      }

      // Filter and sort cards based on review options
      let filteredCards = [...cachedCards];

      // Apply filters similar to what the server would do
      if (reviewOptions.prioritizeDifficult) {
        filteredCards.sort((a, b) => (b.difficultyLevel || 0) - (a.difficultyLevel || 0));
      }

      // Validate card data to prevent runtime errors
      filteredCards = filteredCards.filter(card => {
        if (!card || typeof card !== 'object' || !card.id || !card.question) {
          console.warn("Filtered out invalid card:", card);
          return false;
        }
        return true;
      });

      if (reviewOptions.includeOverdue) {
        const today = new Date();
        filteredCards = filteredCards.filter(card =>
          !card.nextReviewDate || new Date(card.nextReviewDate) <= today
        );
      }

      // Limit the number of cards
      filteredCards = filteredCards.slice(0, reviewOptions.limit || 20);

      const response = {
        cards: filteredCards,
        metadata: {
          sessionId: `offline-${Date.now()}`,
          isOfflineSession: true,
          deckId: deckId || 'global',
          cardCount: filteredCards.length,
          totalCardCount: cachedCards.length
        }
      };
      setIsOfflineSession(true);
      return response;
    } catch (error) {
      console.error("Error fetching offline review cards:", error);
      setIsOfflineSession(true);

      // Provide more detailed error information
      const errorMessage = error.message || "Unknown error occurred";
      toast.error(`Failed to load offline cards: ${errorMessage}`, {
        autoClose: 7000,
        hideProgressBar: false,
      });

      return {
        cards: [],
        metadata: {
          isOfflineSession: true,
          error: errorMessage,
          timestamp: new Date().toISOString(),
          recoveryAttempted: true
        }
      };
    }
  };

  const { data, isLoading, error: reviewCardsError, refetch: refetchReviewCards } = useQuery(
    ["reviewCards", deckId || "global", reviewOptions],
    fetchReviewCards,
    {
      retry: 2,
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 10000),
      onError: (error) => {
        console.error("Error fetching review cards:", error);
        toast.error(
          <div>
            <p>Failed to load review cards.</p>
            <button
              className="mt-2 px-2 py-1 bg-primary-600 text-white rounded text-xs"
              onClick={() => refetchReviewCards()}
            >
              Retry
            </button>
          </div>,
          { autoClose: false, closeOnClick: false }
        );
      },
      onSuccess: (data) => {
        if (data.cards.length === 0) {
          toast.info(
            "All cards are up to date! Come back tomorrow for more review.",
            { icon: <CalendarIcon className="h-5 w-5 text-blue-500" /> }
          );
        } else {
          // Store session metadata
          setSessionMetadata(data.metadata);

          // Update offline session status
          setIsOfflineSession(!!data.metadata?.isOfflineSession);

          // Track session start
          setTimeout(() => {
            trackAnalytics('session_started', {
              totalCards: data.cards.length,
              deckId: deckId || 'global',
              isOfflineSession: !!data.metadata?.isOfflineSession,
              sessionId: data.metadata?.sessionId,
              metadata: {
                totalDueCards: data.metadata?.totalDueCards,
                overdueCards: data.metadata?.overdueCards,
                estimatedMinutesToComplete: data.metadata?.estimatedMinutesToComplete
              }
            });

            // Start the session timer
            setSessionStartTime(Date.now());
            setIsTimerActive(true);
          }, 500);

          // Cache cards for offline use if this is not an offline session
          if (isOnline && (!data.metadata?.isOfflineSession) && data.cards.length > 0) {
            OfflineStorageService.cacheCards(data.cards, deckId)
              .then(result => {
                if (!result.success) {
                  console.error('Error caching cards:', result.message);
                  // Only show toast for significant failures
                  if (result.cachedCount === 0 && result.totalCount > 0) {
                    toast.warning(
                      "Failed to cache cards for offline use. Some features may not work offline.",
                      { autoClose: 5000 }
                    );
                  } else if (result.cachedCount < result.totalCount) {
                    console.warn(`Cached ${result.cachedCount} of ${result.totalCount} cards`);
                  }
                } else if (result.cachedCount > 0) {
                  console.log(`Successfully cached ${result.cachedCount} cards for offline use`);
                }
              })
              .catch(err => {
                console.error('Unexpected error caching cards:', err);
                toast.error("Failed to prepare offline mode. Please try again.");
              });
          }
        }
      },
    }
  );

  // Extract cards from the response
  const reviewCards = data?.cards || [];

  // Get deck info if in single-deck mode
  const { data: deck, error: deckError } = useQuery(
    ["deck", deckId],
    () => decksAPI.getDeckById(deckId),
    {
      enabled: !!deckId,
      onError: (error) => {
        console.error("Error fetching deck:", error);
        toast.error("Failed to load deck information. Please try again.");
      }
    }
  );

  // Monitor online status and check for unsynced reviews
  useEffect(() => {
    const handleOnline = () => {
      setIsOnline(true);
      // When coming back online, refresh the data if we were using offline data
      if (isOfflineSession) {
        toast.info("You're back online! Refreshing data...");
        // Refresh the review cards
        queryClient.invalidateQueries(["reviewCards", deckId || "global", reviewOptions]);
      }
    };

    const handleOffline = () => {
      setIsOnline(false);
      toast.warning("You're offline. Your progress will be saved locally and synced when you're back online.");
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // Initialize the offline storage service
    OfflineStorageService.initialize().catch(error => {
      console.error("Failed to initialize offline storage:", error);
      toast.error("Failed to initialize offline storage. Some offline features may not work properly.");
    });

    // Check for unsynced reviews
    const checkUnsyncedReviews = async () => {
      try {
        const count = await OfflineStorageService.getUnsyncedReviewCount();
        setUnsyncedCount(count);
      } catch (error) {
        console.error("Error checking unsynced reviews:", error);
      }
    };

    checkUnsyncedReviews();

    // Set up periodic checking for unsynced reviews
    const interval = setInterval(checkUnsyncedReviews, 60000); // Check every minute

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
      clearInterval(interval);
    };
  }, [deckId, isOfflineSession, queryClient, reviewOptions]);

  // Session timer
  useEffect(() => {
    let timerInterval;

    if (isTimerActive && sessionStartTime) {
      // Update timer every second
      timerInterval = setInterval(() => {
        const elapsedSeconds = Math.floor((Date.now() - sessionStartTime) / 1000);
        setSessionTime(elapsedSeconds);
      }, 1000);
    }

    return () => {
      if (timerInterval) {
        clearInterval(timerInterval);
      }
    };
  }, [isTimerActive, sessionStartTime]);

  // Format time for display (MM:SS or HH:MM:SS)
  const formatSessionTime = (seconds) => {
    if (seconds < 0) return "00:00";

    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;

    if (hours > 0) {
      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    } else {
      return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
  };

  // Generate motivational messages based on progress and performance
  const generateMotivationalMessage = useCallback(() => {
    // Messages for different progress stages
    const startingMessages = [
      "Let's get started! You've got this!",
      "Ready to boost your knowledge? Let's go!",
      "Your brain is ready for some exercise!",
      "Time to strengthen those neural connections!",
      "Every card you review is a step toward mastery!"
    ];

    const midwayMessages = [
      "You're making great progress! Keep it up!",
      "Halfway there! You're doing great!",
      "Keep going! Your future self will thank you!",
      "You're on a roll! Don't stop now!",
      "Consistency is key to learning. You're nailing it!"
    ];

    const almostDoneMessages = [
      "Almost there! Just a few more cards to go!",
      "The finish line is in sight! Keep pushing!",
      "You've come so far! Let's finish strong!",
      "Final stretch! You can do this!",
      "Just a little more effort to complete today's session!"
    ];

    // Messages based on performance
    const goodPerformanceMessages = [
      "Impressive recall! Your memory is getting stronger!",
      "You're mastering these cards! Great job!",
      "Your hard work is paying off! Keep it up!",
      "Excellent progress! You're really getting this!",
      "Your knowledge is growing with every card!"
    ];

    const improvingMessages = [
      "You're improving with every card!",
      "Progress isn't always perfect, but you're getting there!",
      "Learning is a journey, and you're on the right path!",
      "Each review makes these concepts more familiar!",
      "Keep at it! Spaced repetition works wonders over time!"
    ];

    // Determine which message to show based on progress and performance
    const progress = reviewCards.length > 1 ? currentCardIndex / (reviewCards.length - 1) : 0;
    const performance = sessionStats.reviewed > 0 ?
      (sessionStats.easy + sessionStats.medium) / sessionStats.reviewed : 0;

    let messagePool = [];

    // Select message pool based on progress
    if (progress < 0.3) {
      messagePool = startingMessages;
    } else if (progress < 0.7) {
      messagePool = midwayMessages;
    } else {
      messagePool = almostDoneMessages;
    }

    // Add performance-based messages if we have enough data
    if (sessionStats.reviewed >= 5) {
      if (performance > 0.7) {
        messagePool = [...messagePool, ...goodPerformanceMessages];
      } else {
        messagePool = [...messagePool, ...improvingMessages];
      }
    }

    // Select a random message from the pool
    const randomIndex = Math.floor(Math.random() * messagePool.length);
    return messagePool[randomIndex];
  }, [currentCardIndex, reviewCards.length, sessionStats]);

  // Update motivational message periodically
  useEffect(() => {
    if (reviewCards.length === 0 || sessionComplete || isLoading) {
      return;
    }

    // Set initial message
    setMotivationalMessage(generateMotivationalMessage());

    // Update message every 30 seconds or when card changes
    const messageInterval = setInterval(() => {
      setMotivationalMessage(generateMotivationalMessage());
    }, 30000);

    return () => clearInterval(messageInterval);
  }, [generateMotivationalMessage, reviewCards.length, sessionComplete, isLoading, currentCardIndex]);

  // Add keyboard shortcuts
  useEffect(() => {
    // Only enable shortcuts when we have cards and aren't in session complete state
    if (reviewCards.length === 0 || sessionComplete || isLoading) {
      return;
    }

    const handleKeyDown = (e) => {
      // Don't trigger shortcuts if user is typing in an input field
      if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') {
        return;
      }

      switch (e.key) {
        case ' ':  // Spacebar
        case 'Enter':
          // Toggle card flip
          e.preventDefault();
          setIsFlipped(!isFlipped);
          break;
        case 'ArrowRight':
          // Next card (if not at the end)
          if (currentCardIndex < reviewCards.length - 1) {
            // If card is flipped, flip it back first with a delay to match animation duration before moving
            if (isFlipped) {
              setCardFlipKey(prev => prev + 1); // Reset card to show flip animation
              setIsFlipped(false);
              setTimeout(() => {
                setCurrentCardIndex(currentCardIndex + 1);
                setShowHint(false);
              }, 650); // Wait for flip animation to complete (600ms + 50ms buffer)
            } else {
              setCurrentCardIndex(currentCardIndex + 1);
              setIsFlipped(false);
              setShowHint(false);
            }
          }
          break;
        case 'ArrowLeft':
          // Previous card (if not at the beginning)
          if (currentCardIndex > 0) {
            // If card is flipped, flip it back first with a delay to match animation duration before moving
            if (isFlipped) {
              setCardFlipKey(prev => prev + 1); // Reset card to show flip animation
              setIsFlipped(false);
              setTimeout(() => {
                setCurrentCardIndex(currentCardIndex - 1);
                setShowHint(false);
              }, 650); // Wait for flip animation to complete (600ms + 50ms buffer)
            } else {
              setCurrentCardIndex(currentCardIndex - 1);
              setIsFlipped(false);
              setShowHint(false);
            }
          }
          break;
        case 'h':
          // Show hint
          if (!isFlipped && !showHint) {
            setShowHint(true);
          }
          break;
        case '0':
        case '1':
          // Rate as hard (if card is flipped)
          if (isFlipped) {
            // Convert 0-1 to 0-1 (hard)
            handleGradeResponse(parseInt(e.key));
          }
          break;
        case '2':
        case '3':
          // Rate as medium (if card is flipped)
          if (isFlipped) {
            // Convert 2-3 to 2-3 (medium)
            handleGradeResponse(parseInt(e.key));
          }
          break;
        case '4':
        case '5':
          // Rate as easy (if card is flipped)
          if (isFlipped) {
            // Convert 4-5 to 4-5 (easy)
            handleGradeResponse(parseInt(e.key));
          }
          break;
        default:
          break;
      }
    };

    window.addEventListener('keydown', handleKeyDown);

    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [reviewCards, currentCardIndex, isFlipped, showHint, sessionComplete, isLoading]);

  // Mutation to record card performance using the API
  const recordPerformanceMutation = useMutation(
    async (data) => {
      try {
        // Ensure performance rating is in the correct scale (1-5 for backend)
        // Frontend uses 1-5, backend expects 1-5, so use as-is
        const backendPerformance = Math.max(1, Math.min(5, data.performance));

        // If online, send directly to server
        if (isOnline) {
          // Use enhanced performance recording for better tracking
          const enhancedData = {
            cardId: data.cardId,
            performance: backendPerformance, // Convert UI 1-5 to backend 0-5 scale
            studyTimeSeconds: data.studyTimeSeconds || 0,
            errorType: data.errorType || null,
            confidence: data.confidence || 3,
            notes: data.notes || null,
            answerQuality: data.answerQuality || "hesitant"
          };

          return await studyAPI.recordEnhancedCardPerformance(enhancedData);
        } else {
          // Calculate study time (if available)
          const studyTimeSeconds = data.studyTimeSeconds ||
            (data.startTime ? Math.round((performance.now() - data.startTime) / 1000) : 0);

          // If offline, store locally for later sync with enhanced metadata
          const reviewItem = {
            cardId: data.cardId,
            performance: backendPerformance, // Use backend scale for consistency
            studyTimeSeconds: studyTimeSeconds,
            errorType: data.errorType,
            confidence: data.confidence || 3,
            notes: data.notes,
            reviewTimestamp: new Date().toISOString(),
            deckId: deckId || null,
            answerQuality: data.answerQuality || "hesitant",
            sessionId: sessionMetadata?.sessionId
          };

          // Store with the enhanced service
          await OfflineStorageService.storeReview(reviewItem);

          // Update the unsynced count
          setUnsyncedCount(await OfflineStorageService.getUnsyncedReviewCount());

          // Return a mock response for offline mode
          return {
            success: true,
            message: "Stored offline for later synchronization",
            nextReviewDate: null, // We don't know this offline
            intervalDays: null,
            reviewId: null
          };
        }
      } catch (error) {
        console.error("Error recording performance:", error);

        // Even if there's an error, try to save offline as a fallback
        if (error.message?.includes('network') || !isOnline) {
          try {
            // Use consistent performance scale for offline storage
            const backendPerformance = Math.max(1, Math.min(5, data.performance));

            const reviewItem = {
              cardId: data.cardId,
              performance: backendPerformance, // Use backend scale for consistency
              studyTimeSeconds: data.studyTimeSeconds || 0,
              reviewTimestamp: new Date().toISOString(),
              errorType: 'api_error',
              errorDetails: error.message,
              confidence: data.confidence || 3,
              notes: data.notes || null,
              answerQuality: data.answerQuality || "hesitant",
              deckId: deckId || null,
              sessionId: sessionMetadata?.sessionId
            };

            await OfflineStorageService.storeReview(reviewItem);
            setUnsyncedCount(await OfflineStorageService.getUnsyncedReviewCount());

            toast.warning("Couldn't reach the server. Your progress has been saved offline.");

            return {
              success: true,
              message: "Stored offline due to error",
              wasOfflineFallback: true
            };
          } catch (offlineError) {
            console.error("Failed to save offline:", offlineError);
            throw error; // Throw the original error
          }
        }

        throw error;
      }
    },
    {
      onSuccess: (result) => {
        if (isOnline && !result.wasOfflineFallback) {
          queryClient.invalidateQueries(["reviewCards", deckId || "global", reviewOptions]);
        } else if (result.wasOfflineFallback) {
          // Don't show a toast as we already showed one in the try/catch
        } else {
          toast.info("Your progress has been saved offline and will sync when you're back online.");
        }
      },
      onError: (error) => {
        console.error("Error recording performance:", error);
        toast.error("Failed to save your progress. Please try again.");
      },
    }
  );

  const handleFlip = (flipped) => {
    setIsFlipped(flipped);
  };

  // Track when the card was flipped to calculate study time
  const [cardFlipTime, setCardFlipTime] = useState(null);

  // Update flip time when card is flipped
  useEffect(() => {
    if (isFlipped) {
      setCardFlipTime(performance.now());
    } else {
      setCardFlipTime(null);
    }
  }, [isFlipped]);

  // Get current card with validation - must be calculated before any conditional returns
  const currentCard = reviewCards[currentCardIndex];

  // Validate current card to prevent rendering errors
  const isValidCard = currentCard &&
    typeof currentCard === 'object' &&
    currentCard.id &&
    (typeof currentCard.question === 'string' || typeof currentCard.answer === 'string');

  // Handle invalid card data - must be called before any conditional returns to maintain hook order
  useEffect(() => {
    if (reviewCards && reviewCards.length > 0 && !isValidCard && !sessionComplete) {
      console.error("Invalid card data encountered:", currentCard);
      toast.error("Invalid card data encountered. Skipping to next card.");

      // Skip to next card if possible
      if (currentCardIndex < reviewCards.length - 1) {
        setCurrentCardIndex(currentCardIndex + 1);
      } else if (reviewCards.length > 0) {
        // If this was the last card, end the session
        setSessionComplete(true);
      }
    }
  }, [currentCardIndex, reviewCards, sessionComplete, isValidCard, currentCard]);

  // Track analytics for study sessions
  const trackAnalytics = useCallback(async (eventType, eventData = {}) => {
    if (!isOnline) {
      // Store analytics events for later sync when offline
      try {
        const analyticsEvent = {
          eventType,
          eventData: {
            ...eventData,
            timestamp: new Date().toISOString(),
            deckId: deckId || 'global',
            sessionId: sessionMetadata?.sessionId || `session-${Date.now()}`,
            isOfflineSession
          }
        };

        // Store in local storage for now (could be enhanced to use IndexedDB)
        const storedEvents = JSON.parse(localStorage.getItem('pendingAnalyticsEvents') || '[]');
        storedEvents.push(analyticsEvent);
        localStorage.setItem('pendingAnalyticsEvents', JSON.stringify(storedEvents));

        console.log('Stored offline analytics event:', eventType);
      } catch (error) {
        console.error('Failed to store offline analytics event:', error);
      }
      return;
    }

    // Send analytics event to server when online
    try {
      // This is a simplified example - in a real app, you'd have a proper analytics API
      await analyticsAPI.trackEvent(eventType, {
        ...eventData,
        deckId: deckId || 'global',
        sessionId: sessionMetadata?.sessionId
      });
    } catch (error) {
      console.error('Failed to track analytics event:', error);
    }
  }, [deckId, isOnline, isOfflineSession, sessionMetadata]);

  const handleGradeResponse = (performance, additionalData = {}) => {
    // Only allow grading if card is flipped
    if (!isFlipped) return;

    const currentCard = reviewCards[currentCardIndex];

    // Validate performance rating (should be 1-5 from UI)
    const validatedPerformance = Math.max(1, Math.min(5, performance));

    // Calculate study time (time since card was flipped)
    const studyTimeSeconds = cardFlipTime
      ? Math.round((performance.now() - cardFlipTime) / 1000)
      : 0;

    // Update statistics based on performance (using UI scale 1-5)
    setSessionStats((prev) => {
      const newStats = {
        ...prev,
        reviewed: prev.reviewed + 1,
      };

      if (validatedPerformance <= 2) {
        newStats.hard = prev.hard + 1;
        newStats.streak = 0;
      } else if (validatedPerformance <= 4) {
        newStats.medium = prev.medium + 1;
        newStats.streak = prev.streak + 1;
      } else {
        newStats.easy = prev.easy + 1;
        newStats.streak = prev.streak + 1;
      }

      // Check if we should celebrate streak
      if (newStats.streak > 0 && newStats.streak % 5 === 0) {
        setCelebrateStreak(true);
        setTimeout(() => setCelebrateStreak(false), 3000);
      }

      return newStats;
    });

    // Track analytics for this card review
    trackAnalytics('card_reviewed', {
      cardId: currentCard.id,
      performance: validatedPerformance,
      studyTimeSeconds,
      difficultyLevel: currentCard.difficultyLevel,
      cardIndex: currentCardIndex,
      confidence: additionalData.confidence || null,
      errorType: additionalData.errorType || null
    });

    // Record performance with enhanced data (using validated performance)
    recordPerformanceMutation.mutate({
      cardId: currentCard.id,
      performance: validatedPerformance, // This will be converted to backend scale in the mutation
      studyTimeSeconds: studyTimeSeconds,
      startTime: cardFlipTime,
      confidence: additionalData.confidence || 3,
      errorType: additionalData.errorType || null,
      notes: additionalData.notes || null,
      answerQuality: additionalData.answerQuality || "hesitant"
    });

    // Move to next card or finish if this was the last one
    if (currentCardIndex < reviewCards.length - 1) {
      setCurrentCardIndex(currentCardIndex + 1);
      setIsFlipped(false);
      setShowHint(false);
    } else {
      // Session finished
      setSessionComplete(true);

      // Stop the timer
      setIsTimerActive(false);

      // Track session completion with actual session time
      trackAnalytics('session_completed', {
        totalCards: reviewCards.length,
        easyCount: sessionStats.easy + (validatedPerformance >= 5 ? 1 : 0),
        mediumCount: sessionStats.medium + (validatedPerformance >= 3 && validatedPerformance <= 4 ? 1 : 0),
        hardCount: sessionStats.hard + (validatedPerformance <= 2 ? 1 : 0),
        totalTimeSeconds: sessionTime,
        averageTimePerCard: Math.round(sessionTime / (sessionStats.reviewed + 1)),
        completionTimestamp: new Date().toISOString()
      });
    }
  };

  const handleShowHint = () => {
    setShowHint(true);
  };

  const handleRestartSession = () => {
    // Track session restart
    trackAnalytics('session_restarted', {
      previousStats: {
        reviewed: sessionStats.reviewed,
        easy: sessionStats.easy,
        medium: sessionStats.medium,
        hard: sessionStats.hard,
        streak: sessionStats.streak
      },
      deckId: deckId || 'global',
      totalCards: reviewCards.length
    });

    // Reset session state
    setCurrentCardIndex(0);
    setIsFlipped(false);
    setShowHint(false);
    setSessionComplete(false);
    setSessionStats({
      reviewed: 0,
      easy: 0,
      medium: 0,
      hard: 0,
      streak: 0,
    });

    // Reset and restart the timer
    setSessionTime(0);
    setSessionStartTime(Date.now());
    setIsTimerActive(true);
  };

  const generateHint = (answer) => {
    // Handle null, undefined, or empty answers
    if (!answer || typeof answer !== 'string') {
      return "Try to recall the answer...";
    }

    try {
      // More sophisticated hint generation algorithm
      // 1. For short answers, reveal fewer characters
      // 2. For longer answers, reveal key parts like first letters of words
      // 3. Always show punctuation and spaces
      // 4. Reveal a consistent percentage of characters based on answer length

      // Remove HTML tags if present
      const plainAnswer = answer.replace(/<[^>]*>/g, '');

      if (plainAnswer.trim() === '') {
        return "Try to recall the answer...";
      }

      // Split into words
      const words = plainAnswer.split(/\s+/);

      // Different strategies based on answer length
      if (plainAnswer.length <= 5) {
        // Very short answer - just show the first letter
        return plainAnswer.charAt(0) + plainAnswer.substring(1).replace(/[a-zA-Z0-9]/g, '_');
      } else if (words.length <= 2 && plainAnswer.length < 15) {
        // Short answer - show first letter and some random letters
        return plainAnswer.replace(/[a-zA-Z0-9]/g, (char, index) => {
          // Show first letter, every 4th letter, and non-alphanumeric chars
          return index === 0 || index % 4 === 0 || !/[a-zA-Z0-9]/.test(char) ? char : '_';
        });
      } else {
        // Longer answer - show first letter of each word and keep structure
        let result = '';
        let inWord = false;
        let wordIndex = 0;

        for (let i = 0; i < plainAnswer.length; i++) {
          const char = plainAnswer[i];
          const isAlphaNumeric = /[a-zA-Z0-9]/.test(char);

          if (isAlphaNumeric) {
            if (!inWord) {
              // Start of a new word - show the first letter
              result += char;
              inWord = true;
              wordIndex = 1;
            } else {
              // In the middle of a word
              // Show some letters based on position and word length
              if (wordIndex % 3 === 0 || Math.random() < 0.2) {
                result += char;
              } else {
                result += '_';
              }
              wordIndex++;
            }
          } else {
            // Non-alphanumeric character (space, punctuation) - always show
            result += char;
            inWord = false;
          }
        }

        return result;
      }
    } catch (error) {
      console.error("Error generating hint:", error);
      return "Try to recall the answer...";
    }
  };

  // Generate next review date display text
  const getNextReviewText = (card) => {
    if (!card || !card.nextReviewDate) return "Not scheduled";

    try {
      // Validate the date format
      const nextDateStr = card.nextReviewDate;
      if (typeof nextDateStr !== 'string' && !(nextDateStr instanceof Date)) {
        console.warn("Invalid nextReviewDate format:", nextDateStr);
        return "Not scheduled";
      }

      const nextDate = new Date(nextDateStr);

      // Check if the date is valid
      if (isNaN(nextDate.getTime())) {
        console.warn("Invalid date value:", nextDateStr);
        return "Not scheduled";
      }

      const today = new Date();
      const tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);

      if (nextDate.toDateString() === today.toDateString()) return "Later today";
      if (nextDate.toDateString() === tomorrow.toDateString()) return "Tomorrow";

      // Calculate days difference
      const diffTime = Math.abs(nextDate - today);
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

      if (diffDays <= 7) return `In ${diffDays} days`;
      if (diffDays <= 30) return `In ${Math.floor(diffDays / 7)} weeks`;
      return `In ${Math.floor(diffDays / 30)} months`;
    } catch (error) {
      console.error("Error calculating next review date:", error);
      return "Not scheduled";
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="inline-block animate-spin rounded-full h-12 w-12 border-t-4 border-b-4 border-primary-600 mb-4"></div>
          <p className="text-lg text-gray-700 dark:text-gray-300">
            Loading your review session...
          </p>
        </div>
      </div>
    );
  }

  if (!reviewCards || reviewCards.length === 0) {
    return (
      <div className="max-w-4xl mx-auto py-8 px-4">
        <div className="mb-6 flex items-center justify-between">
          <div className="flex items-center">
            <Link
              to={deckId ? `/decks/${deckId}` : "/dashboard"}
              className="text-primary-600 hover:text-primary-800 mr-2"
            >
              <ArrowLeftIcon className="h-5 w-5 inline" />
            </Link>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              {deckId ? `Review: ${deck?.title || "Loading..."}` : "Daily Review"}
            </h1>
          </div>

          {/* Offline indicator */}
          {(!isOnline || isOfflineSession) && (
            <div className="flex items-center bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300 px-3 py-1 rounded-full text-sm">
              <CloudIcon className="h-4 w-4 mr-1.5" />
              {!isOnline ? "Offline Mode" : "Using Cached Data"}
            </div>
          )}
        </div>

        <motion.div
          className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden p-8 text-center"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
        >
          <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-green-100 dark:bg-green-900 mb-4">
            <CheckCircleIcon className="h-8 w-8 text-green-600 dark:text-green-400" />
          </div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
            All caught up!
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mb-8">
            You've reviewed all your cards for now. Come back tomorrow for more
            review cards.
          </p>
          <div className="flex justify-center space-x-4">
            <Link to={deckId ? `/decks/${deckId}` : "/dashboard"}>
              <Button variant="secondary">
                {deckId ? "Back to Deck" : "Back to Dashboard"}
              </Button>
            </Link>
            <Link to="/app/discover">
              <Button variant="primary">Find New Decks</Button>
            </Link>
          </div>
        </motion.div>
      </div>
    );
  }

  // Session completed view
  if (sessionComplete) {
    const totalReviewed = sessionStats.reviewed;
    const accuracyPercent = Math.round(
      ((sessionStats.easy + sessionStats.medium) / totalReviewed) * 100
    );

    return (
      <div className="max-w-4xl mx-auto py-8 px-4">
        <div className="mb-6 flex items-center">
          <Link
            to={deckId ? `/decks/${deckId}` : "/dashboard"}
            className="text-primary-600 hover:text-primary-800 mr-2"
          >
            <ArrowLeftIcon className="h-5 w-5 inline" />
          </Link>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            Review Complete
          </h1>
        </div>

        <motion.div
          className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
        >
          <div className="p-8 text-center">
            <CheckCircleIcon className="h-16 w-16 text-green-500 mx-auto mb-4" />
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
              Great job!
            </h2>
            <p className="text-lg text-gray-600 dark:text-gray-400 mb-6">
              You've completed your spaced repetition review session
            </p>

            <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
              <div className="bg-blue-50 dark:bg-blue-900/30 rounded-lg p-4">
                <p className="text-blue-800 dark:text-blue-300 text-sm font-medium">
                  Cards Reviewed
                </p>
                <p className="text-3xl font-bold text-blue-700 dark:text-blue-400">
                  {totalReviewed}
                </p>
              </div>
              <div className="bg-green-50 dark:bg-green-900/30 rounded-lg p-4">
                <p className="text-green-800 dark:text-green-300 text-sm font-medium">
                  Accuracy
                </p>
                <p className="text-3xl font-bold text-green-700 dark:text-green-400">
                  {accuracyPercent}%
                </p>
              </div>
              <div className="bg-purple-50 dark:bg-purple-900/30 rounded-lg p-4">
                <p className="text-purple-800 dark:text-purple-300 text-sm font-medium">
                  Best Streak
                </p>
                <p className="text-3xl font-bold text-purple-700 dark:text-purple-400">
                  {sessionStats.streak}
                </p>
              </div>
              <div className="bg-indigo-50 dark:bg-indigo-900/30 rounded-lg p-4">
                <p className="text-indigo-800 dark:text-indigo-300 text-sm font-medium">
                  Total Time
                </p>
                <p className="text-3xl font-bold text-indigo-700 dark:text-indigo-400">
                  {formatSessionTime(sessionTime)}
                </p>
                <p className="text-xs text-indigo-600 dark:text-indigo-400 mt-1">
                  {Math.round(sessionTime / totalReviewed)}s per card
                </p>
              </div>
            </div>

            <div className="mb-8">
              <h3 className="text-md font-medium text-gray-900 dark:text-white mb-2 text-left">
                Performance Breakdown
              </h3>
              <div className="bg-gray-100 dark:bg-gray-700 rounded-lg p-4">
                <div className="flex justify-between mb-1 text-sm">
                  <span className="text-green-700 dark:text-green-400">
                    Easy: {sessionStats.easy}
                  </span>
                  <span className="text-yellow-700 dark:text-yellow-400">
                    Medium: {sessionStats.medium}
                  </span>
                  <span className="text-red-700 dark:text-red-400">
                    Hard: {sessionStats.hard}
                  </span>
                </div>
                <div className="w-full h-3 bg-gray-200 dark:bg-gray-600 rounded-full overflow-hidden">
                  <div className="flex h-full">
                    <div
                      className="bg-green-500 h-full"
                      style={{
                        width: `${(sessionStats.easy / totalReviewed) * 100}%`,
                      }}
                    ></div>
                    <div
                      className="bg-yellow-500 h-full"
                      style={{
                        width: `${
                          (sessionStats.medium / totalReviewed) * 100
                        }%`,
                      }}
                    ></div>
                    <div
                      className="bg-red-500 h-full"
                      style={{
                        width: `${(sessionStats.hard / totalReviewed) * 100}%`,
                      }}
                    ></div>
                  </div>
                </div>
              </div>
            </div>

            <div className="flex flex-wrap justify-center gap-4">
              <Button variant="primary" onClick={handleRestartSession}>
                Review Again
              </Button>
              <Link to={deckId ? `/decks/${deckId}` : "/dashboard"}>
                <Button variant="secondary">
                  {deckId ? "Back to Deck" : "Back to Dashboard"}
                </Button>
              </Link>
            </div>
          </div>
        </motion.div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto py-8 px-4">
      {/* Show confetti animation when reaching streak milestones */}
      {celebrateStreak && (
        <Confetti
          particleCount={4}
          spread={70}
          colors={["#ef4444", "#f87171", "#fca5a5", "#fecaca", "#fee2e2"]}
        />
      )}

      <div className="mb-6 flex flex-wrap items-center justify-between gap-2">
        <div className="flex items-center">
          <Link
            to={deckId ? `/decks/${deckId}` : "/dashboard"}
            className="text-primary-600 hover:text-primary-800 mr-2"
          >
            <ArrowLeftIcon className="h-5 w-5 inline" />
          </Link>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            {deckId ? `Review: ${deck?.title || "Loading..."}` : "Daily Review"}
          </h1>

          {/* Online/Offline indicator */}
          <div className="ml-3 flex items-center">
            <span className={`inline-block w-2 h-2 rounded-full mr-1 ${isOnline ? 'bg-green-500' : 'bg-red-500'}`}></span>
            <span className="text-sm text-gray-600 dark:text-gray-400">
              {isOnline ? (isOfflineSession ? 'Using Cached Data' : 'Online') : 'Offline'}
            </span>
          </div>

          {/* Offline session badge */}
          {isOfflineSession && (
            <div className="ml-2 px-2 py-0.5 bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300 rounded-full text-xs">
              <CloudIcon className="h-3 w-3 inline mr-1" />
              Offline Session
            </div>
          )}
        </div>

        <div className="flex items-center space-x-2">
          {/* Enhanced Sync Manager */}
          <div className="mr-2">
            <EnhancedSyncManager
              onSyncComplete={useCallback((result) => {
                if (result.syncedCount > 0) {
                  // Refresh the review cards after sync
                  queryClient.invalidateQueries(["reviewCards", deckId || "global", reviewOptions]);
                }
              }, [deckId, queryClient, reviewOptions])}
            />
          </div>
          <Button
            variant="ghost"
            size="sm"
            icon={AdjustmentsHorizontalIcon}
            onClick={() => setShowOptions(!showOptions)}
          >
            <span className="hidden sm:inline">Options</span>
          </Button>
          <Button
            variant="ghost"
            size="sm"
            icon={InformationCircleIcon}
            onClick={() => setShowTutorial(true)}
          >
            <span className="hidden sm:inline">How It Works</span>
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowKeyboardShortcuts(true)}
          >
            <span className="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M3 5a2 2 0 012-2h10a2 2 0 012 2v8a2 2 0 01-2 2h-2.22l.123.489.804.804A1 1 0 0113 18H7a1 1 0 01-.707-1.707l.804-.804L7.22 15H5a2 2 0 01-2-2V5zm5.771 7H5V5h10v7H8.771z" clipRule="evenodd" />
              </svg>
              <span className="hidden sm:inline">Shortcuts</span>
            </span>
          </Button>
          <Button
            variant={useEnhancedMode ? "primary" : "secondary"}
            size="sm"
            onClick={() => setUseEnhancedMode(!useEnhancedMode)}
          >
            <span className="text-xs">{useEnhancedMode ? "Advanced Mode" : "Basic Mode"}</span>
          </Button>
          {sessionStats.streak > 0 && (
            <div className="px-3 py-1 bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-300 rounded-full flex items-center text-sm">
              <FireIcon className="h-4 w-4 mr-1" />
              <span>Streak: {sessionStats.streak}</span>
            </div>
          )}
        </div>
      </div>

      {/* Review options panel */}
      {showOptions && (
        <div className="mb-6 bg-white dark:bg-gray-800 rounded-lg shadow-md p-4">
          <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Review Options</h3>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm text-gray-700 dark:text-gray-300 mb-1">Cards per session</label>
              <select
                className="w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-800 text-sm"
                value={reviewOptions.limit || 20}
                onChange={(e) => setReviewOptions({...reviewOptions, limit: parseInt(e.target.value)})}
              >
                <option value={10}>10 cards</option>
                <option value={20}>20 cards</option>
                <option value={30}>30 cards</option>
                <option value={50}>50 cards</option>
                <option value={100}>100 cards</option>
              </select>
            </div>
            <div>
              <label className="block text-sm text-gray-700 dark:text-gray-300 mb-1">Sort by</label>
              <select
                className="w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-800 text-sm"
                value={reviewOptions.sortBy}
                onChange={(e) => setReviewOptions({...reviewOptions, sortBy: e.target.value})}
              >
                <option value="difficulty">Difficulty (hardest first)</option>
                <option value="overdue">Overdue (oldest first)</option>
                <option value="random">Random order</option>
              </select>
            </div>
            <div className="flex items-center">
              <input
                type="checkbox"
                id="prioritizeDifficult"
                checked={reviewOptions.prioritizeDifficult}
                onChange={(e) => setReviewOptions({...reviewOptions, prioritizeDifficult: e.target.checked})}
                className="rounded text-primary-600 focus:ring-primary-500"
              />
              <label htmlFor="prioritizeDifficult" className="ml-2 text-sm text-gray-700 dark:text-gray-300">
                Prioritize difficult cards
              </label>
            </div>
            <div className="flex items-center">
              <input
                type="checkbox"
                id="includeCollaborative"
                checked={reviewOptions.includeCollaborative}
                onChange={(e) => setReviewOptions({...reviewOptions, includeCollaborative: e.target.checked})}
                className="rounded text-primary-600 focus:ring-primary-500"
              />
              <label htmlFor="includeCollaborative" className="ml-2 text-sm text-gray-700 dark:text-gray-300">
                Include collaborative decks
              </label>
            </div>
            <div className="flex items-center">
              <input
                type="checkbox"
                id="useEnhancedMode"
                checked={useEnhancedMode}
                onChange={(e) => setUseEnhancedMode(e.target.checked)}
                className="rounded text-primary-600 focus:ring-primary-500"
              />
              <label htmlFor="useEnhancedMode" className="ml-2 text-sm text-gray-700 dark:text-gray-300">
                Use advanced study mode
              </label>
            </div>
          </div>
          <div className="mt-4 flex justify-end">
            <Button
              variant="primary"
              size="sm"
              onClick={() => {
                queryClient.invalidateQueries(["reviewCards", deckId || "global", reviewOptions]);
                setShowOptions(false);
              }}
            >
              Apply Changes
            </Button>
          </div>
        </div>
      )}

      <div className="mb-4 flex flex-wrap items-center justify-between gap-4">
        {/* Progress information */}
        <div className="flex flex-col w-full sm:w-auto">
          <div className="flex items-center justify-between mb-1">
            <span className="text-sm text-gray-600 dark:text-gray-400">
              {currentCardIndex + 1} of {reviewCards.length} cards
            </span>
            <span className="text-xs text-gray-500 dark:text-gray-400">
              {reviewCards.length > 1 ? Math.round((currentCardIndex / (reviewCards.length - 1)) * 100) : 0}% complete
            </span>
          </div>
          <ProgressBar
            value={currentCardIndex}
            max={Math.max(reviewCards.length - 1, 1)}
            showLabel={false}
            height="h-2"
            colorClass={`bg-gradient-to-r from-blue-500 to-primary-600 dark:from-blue-600 dark:to-primary-500`}
            className="w-full sm:w-64"
          />
        </div>

        <div className="flex items-center gap-4 flex-wrap justify-end">
          {/* Session timer */}
          <div className="flex items-center text-sm text-gray-600 dark:text-gray-400 bg-gray-100 dark:bg-gray-800 px-3 py-1 rounded-full">
            <ClockIcon className="h-4 w-4 mr-1" />
            <span>Session time: {formatSessionTime(sessionTime)}</span>
          </div>

          {/* Next review date */}
          <div className="flex items-center text-sm text-gray-600 dark:text-gray-400 bg-gray-100 dark:bg-gray-800 px-3 py-1 rounded-full">
            <CalendarIcon className="h-4 w-4 mr-1" />
            <span>Next review: {getNextReviewText(currentCard)}</span>
          </div>
        </div>
      </div>

      {/* Session metadata */}
      {sessionMetadata && (
        <div className="mb-4 bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm">
          <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Session Overview
          </h3>
          <div className="grid grid-cols-2 sm:grid-cols-4 gap-2 text-xs">
            <div className="flex flex-col">
              <span className="text-gray-500 dark:text-gray-400">Total Due</span>
              <span className="font-medium text-gray-900 dark:text-white">{sessionMetadata.totalDueCards} cards</span>
            </div>
            <div className="flex flex-col">
              <span className="text-gray-500 dark:text-gray-400">Overdue</span>
              <span className="font-medium text-gray-900 dark:text-white">{sessionMetadata.overdueCards} cards</span>
            </div>
            <div className="flex flex-col">
              <span className="text-gray-500 dark:text-gray-400">Est. Time</span>
              <span className="font-medium text-gray-900 dark:text-white">{sessionMetadata.estimatedMinutesToComplete} min</span>
            </div>
            <div className="flex flex-col">
              <span className="text-gray-500 dark:text-gray-400">Next Due</span>
              <span className="font-medium text-gray-900 dark:text-white">
                {sessionMetadata.nextDueDate ? new Date(sessionMetadata.nextDueDate).toLocaleDateString() : 'None'}
              </span>
            </div>
          </div>
        </div>
      )}

      {/* Motivational message */}
      {motivationalMessage && (
        <div className="mb-4 bg-gradient-to-r from-primary-50 to-blue-50 dark:from-primary-900/30 dark:to-blue-900/30 border border-primary-100 dark:border-primary-800 rounded-lg p-3 text-center">
          <p className="text-primary-700 dark:text-primary-300 text-sm font-medium">
            {motivationalMessage}
          </p>
        </div>
      )}

      <div className="mb-6">
        <div
          role="region"
          aria-label="Flashcard"
          aria-live="polite"
          tabIndex="0"
          className="focus:outline-none focus:ring-2 focus:ring-primary-500 rounded-xl"
        >
          {!isValidCard ? (
            // Fallback UI for invalid card data
            <div className="bg-red-50 dark:bg-red-900/30 rounded-xl p-8 text-center">
              <ExclamationCircleIcon className="h-12 w-12 text-red-500 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-red-800 dark:text-red-300 mb-2">
                Invalid Card Data
              </h3>
              <p className="text-red-600 dark:text-red-400 mb-4">
                This card contains invalid data and cannot be displayed.
              </p>
              <div className="flex justify-center space-x-4">
                <Button
                  variant="secondary"
                  onClick={() => {
                    if (currentCardIndex < reviewCards.length - 1) {
                      setCurrentCardIndex(currentCardIndex + 1);
                    } else {
                      setSessionComplete(true);
                    }
                  }}
                >
                  Skip Card
                </Button>
              </div>
            </div>
          ) : useEnhancedMode ? (
            <AdvancedFlashCard
              card={currentCard}
              onNext={handleGradeResponse}
              onPrevious={() => {
                if (currentCardIndex > 0) {
                  // If card is flipped, flip it back first with a delay to match animation duration before moving
                  if (isFlipped) {
                    setCardFlipKey(prev => prev + 1); // Reset card to show flip animation
                    setIsFlipped(false);
                    setTimeout(() => {
                      setCurrentCardIndex(currentCardIndex - 1);
                      setShowHint(false);
                    }, 650); // Wait for flip animation to complete (600ms + 50ms buffer)
                  } else {
                    setCurrentCardIndex(currentCardIndex - 1);
                    setIsFlipped(false);
                    setShowHint(false);
                  }
                }
              }}
              showAnswer={isFlipped}
              showControls={true}
              isReview={true}
              aria-expanded={isFlipped}
              aria-controls="flashcard-answer"
            />
          ) : (
            <FlashCard
              cardKey={`spaced-${currentCard.id}-${currentCardIndex}-${cardFlipKey}`}
              front={currentCard.question || "No question available"}
              back={currentCard.answer || "No answer available"}
              frontImage={null} // Image functionality temporarily disabled
              backImage={null} // Image functionality temporarily disabled
              onFlip={handleFlip}
              onRate={handleGradeResponse}
              difficulty={currentCard.difficultyLevel || 0}
              isFlippable={true}
              showHint={showHint}
              hint={generateHint(currentCard.answer || "")}
              aria-expanded={isFlipped}
              aria-controls="flashcard-answer"
              isSessionCompleted={sessionComplete}
            />
          )}
          {/* Screen reader only text */}
          <div className="sr-only">
            <div>Press space or enter to flip the card</div>
            <div>Press h to show a hint</div>
            <div>Press left arrow to go to the previous card</div>
            <div>Press right arrow to go to the next card</div>
            <div>Press 1 or 2 to rate as hard</div>
            <div>Press 3 or 4 to rate as medium</div>
            <div>Press 5 to rate as easy</div>
          </div>
        </div>
      </div>

      {!useEnhancedMode && (
        <div className="flex justify-between">
          <Button
            variant="secondary"
            disabled={currentCardIndex === 0}
            onClick={() => {
              if (currentCardIndex > 0) {
                // If card is flipped, flip it back first with a delay to match animation duration before moving
                if (isFlipped) {
                  setIsFlipped(false);
                  setTimeout(() => {
                    setCurrentCardIndex(currentCardIndex - 1);
                    setShowHint(false);
                  }, 650); // Wait for flip animation to complete (600ms + 50ms buffer)
                } else {
                  setCurrentCardIndex(currentCardIndex - 1);
                  setIsFlipped(false);
                  setShowHint(false);
                }
              }
            }}
            className="flex items-center"
          >
            <ChevronLeftIcon className="h-5 w-5 mr-1" />
            Previous
          </Button>

          <div className="flex space-x-2">
            {!isFlipped && (
              <Button
                variant="secondary"
                onClick={handleShowHint}
                disabled={showHint}
                className="flex items-center"
              >
                <LightBulbIcon className="h-5 w-5 mr-1" />
                <span className="hidden sm:inline">Hint</span>
              </Button>
            )}

            <Button variant="primary" onClick={() => setIsFlipped(!isFlipped)}>
              {isFlipped ? "Hide Answer" : "Show Answer"}
            </Button>
          </div>

          <Button
            variant="secondary"
            disabled={currentCardIndex >= reviewCards.length - 1}
            onClick={() => {
              if (currentCardIndex < reviewCards.length - 1) {
                // If card is flipped, flip it back first with a delay to match animation duration before moving
                if (isFlipped) {
                  setCardFlipKey(prev => prev + 1); // Reset card to show flip animation
                  setIsFlipped(false);
                  setTimeout(() => {
                    setCurrentCardIndex(currentCardIndex + 1);
                    setShowHint(false);
                  }, 650); // Wait for flip animation to complete (600ms + 50ms buffer)
                } else {
                  setCurrentCardIndex(currentCardIndex + 1);
                  setIsFlipped(false);
                  setShowHint(false);
                }
              }
            }}
            className="flex items-center"
          >
            Next
            <ChevronRightIcon className="h-5 w-5 ml-1" />
          </Button>
        </div>
      )}

      {/* Tutorial Modal */}
      <AnimatePresence>
        {showKeyboardShortcuts && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-50 overflow-y-auto"
            aria-labelledby="modal-title"
            role="dialog"
            aria-modal="true"
          >
            <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
              <div
                className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"
                aria-hidden="true"
                onClick={() => setShowKeyboardShortcuts(false)}
              ></div>

              <span
                className="hidden sm:inline-block sm:align-middle sm:h-screen"
                aria-hidden="true"
              >
                &#8203;
              </span>

              <motion.div
                initial={{ scale: 0.9, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                exit={{ scale: 0.9, opacity: 0 }}
                className="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-md sm:w-full sm:p-6"
              >
                <div>
                  <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-primary-100 dark:bg-primary-900">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-primary-600 dark:text-primary-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M3 5a2 2 0 012-2h10a2 2 0 012 2v8a2 2 0 01-2 2h-2.22l.123.489.804.804A1 1 0 0113 18H7a1 1 0 01-.707-1.707l.804-.804L7.22 15H5a2 2 0 01-2-2V5zm5.771 7H5V5h10v7H8.771z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="mt-3 text-center sm:mt-5">
                    <h3
                      className="text-lg leading-6 font-medium text-gray-900 dark:text-white"
                      id="modal-title"
                    >
                      Keyboard Shortcuts
                    </h3>
                    <div className="mt-4">
                      <div className="grid grid-cols-2 gap-3 text-sm text-gray-700 dark:text-gray-300">
                        <div className="flex items-center">
                          <span className="inline-block px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded mr-2 text-xs font-mono">Space</span>
                          <span>Flip card</span>
                        </div>
                        <div className="flex items-center">
                          <span className="inline-block px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded mr-2 text-xs font-mono">Enter</span>
                          <span>Flip card</span>
                        </div>
                        <div className="flex items-center">
                          <span className="inline-block px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded mr-2 text-xs font-mono">→</span>
                          <span>Next card</span>
                        </div>
                        <div className="flex items-center">
                          <span className="inline-block px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded mr-2 text-xs font-mono">←</span>
                          <span>Previous card</span>
                        </div>
                        <div className="flex items-center">
                          <span className="inline-block px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded mr-2 text-xs font-mono">h</span>
                          <span>Show hint</span>
                        </div>
                        <div className="flex items-center">
                          <span className="inline-block px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded mr-2 text-xs font-mono">1-2</span>
                          <span>Rate as hard</span>
                        </div>
                        <div className="flex items-center">
                          <span className="inline-block px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded mr-2 text-xs font-mono">3-4</span>
                          <span>Rate as medium</span>
                        </div>
                        <div className="flex items-center">
                          <span className="inline-block px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded mr-2 text-xs font-mono">5</span>
                          <span>Rate as easy</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="mt-5 sm:mt-6">
                  <Button
                    variant="primary"
                    className="w-full"
                    onClick={() => setShowKeyboardShortcuts(false)}
                  >
                    Got it
                  </Button>
                </div>
              </motion.div>
            </div>
          </motion.div>
        )}

        {showTutorial && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-50 overflow-y-auto"
            aria-labelledby="modal-title"
            role="dialog"
            aria-modal="true"
          >
            <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
              <div
                className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"
                aria-hidden="true"
                onClick={() => setShowTutorial(false)}
              ></div>

              <span
                className="hidden sm:inline-block sm:align-middle sm:h-screen"
                aria-hidden="true"
              >
                &#8203;
              </span>

              <motion.div
                initial={{ scale: 0.9, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                exit={{ scale: 0.9, opacity: 0 }}
                className="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full sm:p-6"
              >
                <div>
                  <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-primary-100 dark:bg-primary-900">
                    <InformationCircleIcon
                      className="h-6 w-6 text-primary-600 dark:text-primary-400"
                      aria-hidden="true"
                    />
                  </div>
                  <div className="mt-3 text-center sm:mt-5">
                    <h3
                      className="text-lg leading-6 font-medium text-gray-900 dark:text-white"
                      id="modal-title"
                    >
                      How Spaced Repetition Works
                    </h3>
                    <div className="mt-4 space-y-4 text-left">
                      <p className="text-sm text-gray-700 dark:text-gray-300">
                        Spaced repetition is a learning technique that
                        incorporates increasing intervals of time between
                        reviews of previously learned material to exploit the
                        psychological spacing effect.
                      </p>

                      <div className="space-y-2">
                        <p className="text-sm font-medium text-gray-900 dark:text-white">
                          Rating System:
                        </p>
                        <div className="flex items-center px-3 py-2 bg-red-50 dark:bg-red-900/30 rounded">
                          <span className="flex items-center justify-center w-6 h-6 bg-red-100 dark:bg-red-800 rounded-full mr-2">
                            <span className="text-xs font-medium text-red-800 dark:text-red-300">
                              1
                            </span>
                          </span>
                          <span className="text-sm text-red-800 dark:text-red-300">
                            Hard - Review again soon
                          </span>
                        </div>
                        <div className="flex items-center px-3 py-2 bg-yellow-50 dark:bg-yellow-900/30 rounded">
                          <span className="flex items-center justify-center w-6 h-6 bg-yellow-100 dark:bg-yellow-800 rounded-full mr-2">
                            <span className="text-xs font-medium text-yellow-800 dark:text-yellow-300">
                              3
                            </span>
                          </span>
                          <span className="text-sm text-yellow-800 dark:text-yellow-300">
                            Medium - Review in a few days
                          </span>
                        </div>
                        <div className="flex items-center px-3 py-2 bg-green-50 dark:bg-green-900/30 rounded">
                          <span className="flex items-center justify-center w-6 h-6 bg-green-100 dark:bg-green-800 rounded-full mr-2">
                            <span className="text-xs font-medium text-green-800 dark:text-green-300">
                              5
                            </span>
                          </span>
                          <span className="text-sm text-green-800 dark:text-green-300">
                            Easy - Review after a longer interval
                          </span>
                        </div>
                      </div>

                      <div className="mt-4 space-y-2">
                        <p className="text-sm font-medium text-gray-900 dark:text-white">
                          Keyboard Shortcuts:
                        </p>
                        <div className="grid grid-cols-2 gap-2 text-sm text-gray-700 dark:text-gray-300">
                          <div className="flex items-center">
                            <span className="inline-block px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded mr-2 text-xs font-mono">Space</span>
                            <span>Flip card</span>
                          </div>
                          <div className="flex items-center">
                            <span className="inline-block px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded mr-2 text-xs font-mono">→</span>
                            <span>Next card</span>
                          </div>
                          <div className="flex items-center">
                            <span className="inline-block px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded mr-2 text-xs font-mono">←</span>
                            <span>Previous card</span>
                          </div>
                          <div className="flex items-center">
                            <span className="inline-block px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded mr-2 text-xs font-mono">h</span>
                            <span>Show hint</span>
                          </div>
                          <div className="flex items-center">
                            <span className="inline-block px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded mr-2 text-xs font-mono">1-2</span>
                            <span>Rate as hard</span>
                          </div>
                          <div className="flex items-center">
                            <span className="inline-block px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded mr-2 text-xs font-mono">3-4</span>
                            <span>Rate as medium</span>
                          </div>
                          <div className="flex items-center">
                            <span className="inline-block px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded mr-2 text-xs font-mono">5</span>
                            <span>Rate as easy</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="mt-5 sm:mt-6">
                  <Button
                    variant="primary"
                    className="w-full"
                    onClick={() => setShowTutorial(false)}
                  >
                    Got it
                  </Button>
                </div>
              </motion.div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default SpacedRepetitionStudy;
