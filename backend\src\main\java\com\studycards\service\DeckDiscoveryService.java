package com.studycards.service;

import com.studycards.dto.DeckResponse;
import com.studycards.dto.TagSummary;
import com.studycards.model.Deck;
import com.studycards.model.DeckTag;
import com.studycards.model.User;
import com.studycards.repository.CardRepository;
import com.studycards.repository.DeckRepository;
import com.studycards.util.PaginationUtil;
import org.springframework.beans.factory.annotation.Autowired;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class DeckDiscoveryService {

    @Autowired
    private DeckRepository deckRepository;

    @Autowired
    private UserService userService;

    @Autowired
    private CardRepository cardRepository;

    @Autowired
    private DeckService deckService;

    @Autowired
    private SubscriptionStatusService subscriptionStatusService;

    @Cacheable(value = "trendingDecks", key = "#pageable.pageNumber + '_' + #pageable.pageSize + '_' + #createdAfter")
    @Transactional(readOnly = true)
    public Page<DeckResponse> getTrendingDecks(Pageable pageable, LocalDateTime createdAfter) {
        // Get current user if authenticated, null if not (for public access)
        User currentUser = userService.getCurrentUserOrNull();
        // Create a new Pageable without sorting since the query has its own ORDER BY clause
        // IMPORTANT: Don't add any additional sorting since the native query handles it
        Pageable unsortedPageable = PageRequest.of(pageable.getPageNumber(), pageable.getPageSize());
        Page<Deck> decks = deckRepository.findTrendingDecks(createdAfter, unsortedPageable);

        // Filter out decks from creators without active subscriptions
        List<Deck> filteredDecks = decks.getContent().stream()
                .filter(deck -> subscriptionStatusService.hasActiveSubscription(deck.getCreator()))
                .collect(Collectors.toList());

        // Use batch mapping for better performance
        List<DeckResponse> mappedDecks = deckService.mapToDecksResponseBatch(filteredDecks, currentUser);

        // FIXED: Use the original total count from the database, not the filtered size
        // This ensures pagination shows correct total pages even when some results are filtered out
        return new org.springframework.data.domain.PageImpl<>(
                mappedDecks,
                pageable,
                decks.getTotalElements()
        );
    }

    @Cacheable(value = "newDecks", key = "#pageable.pageNumber + '_' + #pageable.pageSize + '_' + #createdAfter")
    @Transactional(readOnly = true)
    public Page<DeckResponse> getNewlyCreatedDecks(Pageable pageable, LocalDateTime createdAfter) {
        // Get current user if authenticated, null if not (for public access)
        User currentUser = userService.getCurrentUserOrNull();

        // Ensure we have proper sorting by createdAt DESC if no sorting is specified
        // or if the sorting contains invalid fields like 'popularity'
        Pageable sortedPageable = ensureValidSorting(pageable);

        Page<Deck> decks;
        if (createdAfter != null) {
            // Use repository method that filters by creation date
            decks = deckRepository.findByIsPublicTrueAndDeletedFalseAndCreatedAtAfter(createdAfter, sortedPageable);
        } else {
            // Use standard method without date filtering
            decks = deckRepository.findByIsPublicTrueAndDeletedFalseOrderByCreatedAtDesc(sortedPageable);
        }

        // Filter out decks from creators without active subscriptions
        List<Deck> filteredDecks = decks.getContent().stream()
                .filter(deck -> subscriptionStatusService.hasActiveSubscription(deck.getCreator()))
                .collect(Collectors.toList());

        // Use batch mapping for better performance
        List<DeckResponse> mappedDecks = deckService.mapToDecksResponseBatch(filteredDecks, currentUser);

        // FIXED: Use the original total count from the database, not the filtered size
        // This ensures pagination shows correct total pages even when some results are filtered out
        return new org.springframework.data.domain.PageImpl<>(
                mappedDecks,
                pageable,
                decks.getTotalElements()
        );
    }

    /**
     * Ensures that the pageable has valid sorting for Deck entity fields.
     * Replaces invalid fields like 'popularity' with 'createdAt' and ensures DESC direction.
     */
    private Pageable ensureValidSorting(Pageable pageable) {
        if (pageable.getSort().isUnsorted()) {
            // Default to createdAt DESC if no sorting specified
            return PageRequest.of(pageable.getPageNumber(), pageable.getPageSize(),
                    Sort.by(Sort.Direction.DESC, "createdAt"));
        }

        // Check if any sort property is invalid (like 'popularity')
        boolean hasInvalidSort = pageable.getSort().stream()
                .anyMatch(order -> "popularity".equals(order.getProperty()) ||
                                 !isValidDeckSortField(order.getProperty()));

        if (hasInvalidSort) {
            // Replace with default sorting
            return PageRequest.of(pageable.getPageNumber(), pageable.getPageSize(),
                    Sort.by(Sort.Direction.DESC, "createdAt"));
        }

        return pageable;
    }

    /**
     * Checks if a field name is a valid sort field for the Deck entity
     */
    private boolean isValidDeckSortField(String fieldName) {
        // Valid Deck entity fields that can be used for sorting
        return "id".equals(fieldName) ||
               "title".equals(fieldName) ||
               "description".equals(fieldName) ||
               "category".equals(fieldName) ||
               "isPublic".equals(fieldName) ||
               "createdAt".equals(fieldName) ||
               "updatedAt".equals(fieldName) ||
               "deleted".equals(fieldName) ||
               "deletedAt".equals(fieldName) ||
               "isFolder".equals(fieldName);
    }

    @Cacheable(value = "decksByTag", key = "#tagName + '_' + #pageable.pageNumber + '_' + #pageable.pageSize + '_' + #createdAfter")
    @Transactional(readOnly = true)
    public Page<DeckResponse> getDecksByTag(String tagName, Pageable pageable, LocalDateTime createdAfter) {
        // Get current user if authenticated, null if not (for public access)
        User currentUser = userService.getCurrentUserOrNull();

        // Check if the pageable has popularity sorting and remove it since our repository methods handle their own sorting
        Pageable effectivePageable = pageable;
        if (pageable.getSort().isSorted() &&
            pageable.getSort().stream().anyMatch(order -> "favoriteCount".equals(order.getProperty()))) {
            // Create unsorted pageable for popularity-based queries
            effectivePageable = PageRequest.of(pageable.getPageNumber(), pageable.getPageSize());
        }

        Page<Deck> decks;
        if (createdAfter != null) {
            // Use repository method that filters by tag name and creation date
            decks = deckRepository.findByTagNameIgnoreCaseAndCreatedAtAfter(
                tagName, createdAfter, effectivePageable);
        } else {
            // Use standard method without date filtering
            decks = deckRepository.findByTagNameIgnoreCase(tagName, effectivePageable);
        }

        // Filter out decks from creators without active subscriptions
        List<Deck> filteredDecks = decks.getContent().stream()
                .filter(deck -> subscriptionStatusService.hasActiveSubscription(deck.getCreator()))
                .collect(Collectors.toList());

        // Use batch mapping for better performance
        List<DeckResponse> mappedDecks = deckService.mapToDecksResponseBatch(filteredDecks, currentUser);

        // FIXED: Use the original total count from the database, not the filtered size
        // This ensures pagination shows correct total pages even when some results are filtered out
        return new org.springframework.data.domain.PageImpl<>(
                mappedDecks,
                pageable,
                decks.getTotalElements()
        );
    }

    @Cacheable("popularTags")
    public List<TagSummary> getPopularTags(int limit) {
        return deckRepository.findPopularTags(Pageable.ofSize(limit));
    }

    // Helper method to map Deck to DeckResponse
    public DeckResponse mapToDeckResponse(Deck deck, User currentUser) {
        // CRITICAL SECURITY FIX: Add input validation before delegation
        if (deck == null) {
            throw new IllegalArgumentException("Deck cannot be null");
        }
        if (currentUser == null) {
            throw new IllegalArgumentException("Current user cannot be null");
        }

        try {
            // Delegate to the DeckService for consistent mapping
            return deckService.mapToDeckResponse(deck, currentUser);
        } catch (Exception e) {
            log.error("Error mapping deck {} to response in DeckDiscoveryService for user {}",
                     deck.getId(), currentUser.getId(), e);
            throw e; // Re-throw to maintain error handling contract
        }
    }
}