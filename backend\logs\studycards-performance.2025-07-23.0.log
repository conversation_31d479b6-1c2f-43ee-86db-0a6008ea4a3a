2025-07-23 21:18:09.954 [MessageBroker-9] WARN  PERFORMANCE - 
                [invalidateSearchCaches] [2904ms] [] SLOW_SERVICE_METHOD: CacheUtilityService.silentClear took 2904ms
2025-07-23 21:18:12.233 [MessageBroker-6] ERROR PERFORMANCE - 
                [cleanupOldSearchHistory] [4596ms] [] FAILED_DB_OPERATION: $Proxy243.deleteOldSearchHistory failed after 4596ms with error: InvalidDataAccessApiUsageException
2025-07-23 21:18:14.612 [MessageBroker-10] WARN  PERFORMANCE - 
                [periodicHealthCheck] [7547ms] [] SLOW_SERVICE_METHOD: StatisticsMonitoringService.recordOperationStart took 7547ms
2025-07-23 21:18:14.614 [MessageBroker-16] WARN  PERFORMANCE - 
                [cacheWarming] [4643ms] [] SLOW_SERVICE_METHOD: StatisticsMonitoringService.recordOperationS<PERSON>t took 4643ms
2025-07-23 21:18:14.614 [MessageBroker-9] WARN  PERFORMANCE - 
                [invalidateSearchCaches] [4646ms] [] SLOW_SERVICE_METHOD: CacheUtilityService.silentClear took 4646ms
2025-07-23 21:18:14.615 [MessageBroker-6] WARN  PERFORMANCE - 
                [cleanupOldSearchHistory] [7127ms] [] SLOW_SERVICE_METHOD: SearchPerformanceService.cleanupOldSearchHistory took 7127ms
2025-07-23 21:18:14.617 [MessageBroker-9] WARN  PERFORMANCE - 
                [invalidateSearchCaches] [7567ms] [] SLOW_SERVICE_METHOD: SearchCacheService.invalidateSearchCaches took 7567ms
2025-07-23 21:18:14.618 [MessageBroker-15] WARN  PERFORMANCE - 
                [hourlyPerformanceReport] [7568ms] [] SLOW_SERVICE_METHOD: StatisticsScheduledMonitor.hourlyPerformanceReport took 7568ms
2025-07-23 21:18:14.767 [MessageBroker-4] INFO  PERFORMANCE - 
                [sendVerificationReminders] [154ms] [] DB_OPERATION: $Proxy194.findUnverifiedUsersInTimeRange took 154ms
2025-07-23 21:18:14.941 [MessageBroker-8] INFO  PERFORMANCE - 
                [updateExpiredSubscriptions] [184ms] [] DB_OPERATION: $Proxy194.findBySubscriptionStatusAndSubscriptionEndDateBefore took 184ms
2025-07-23 21:18:14.944 [MessageBroker-4] WARN  PERFORMANCE - 
                [sendVerificationReminders] [7804ms] [] SLOW_SERVICE_METHOD: EmailVerificationReminderService.sendVerificationReminders took 7804ms
2025-07-23 21:18:15.826 [ForkJoinPool.commonPool-worker-26] WARN  PERFORMANCE - 
                [] [1125ms] [] SLOW_DB_OPERATION: $Proxy194.countActiveSubscriptionUsers took 1125ms
2025-07-23 21:18:15.827 [ForkJoinPool.commonPool-worker-27] WARN  PERFORMANCE - 
                [] [1065ms] [] SLOW_DB_OPERATION: $Proxy194.countActiveSubscriptionUsers took 1065ms
2025-07-23 21:18:28.827 [MessageBroker-15] WARN  PERFORMANCE - 
                [hourlyPerformanceReport] [13740ms] [] SLOW_SERVICE_METHOD: StatisticsScheduledMonitor.hourlyPerformanceReport took 13740ms
2025-07-23 21:18:28.840 [MessageBroker-12] WARN  PERFORMANCE - 
                [dailyStatisticsSummary] [13601ms] [] SLOW_SERVICE_METHOD: StatisticsMonitoringService.recordOperationStart took 13601ms
2025-07-23 21:18:28.954 [MessageBroker-14] WARN  PERFORMANCE - 
                [generateDailyNotifications] [14259ms] [] SLOW_DB_OPERATION: $Proxy194.findAll took 14259ms
2025-07-23 21:18:28.954 [MessageBroker-14] WARN  PERFORMANCE - 
                [generateDailyNotifications] [14339ms] [] SLOW_SERVICE_METHOD: UserService.getAllUsers took 14339ms
2025-07-23 21:18:28.964 [ForkJoinPool.commonPool-worker-27] WARN  PERFORMANCE - 
                [] [13137ms] [] SLOW_DB_OPERATION: $Proxy194.countActiveUsersSince took 13137ms
2025-07-23 21:18:28.982 [ForkJoinPool.commonPool-worker-26] WARN  PERFORMANCE - 
                [] [13156ms] [] SLOW_DB_OPERATION: $Proxy194.countActiveUsersSince took 13156ms
2025-07-23 21:18:28.999 [ForkJoinPool.commonPool-worker-28] INFO  PERFORMANCE - 
                [] [117ms] [] DB_OPERATION: $Proxy194.countActiveSubscriptionUsers took 117ms
2025-07-23 21:18:29.654 [MessageBroker-4] WARN  PERFORMANCE - 
                [sendVerificationReminders] [810ms] [] SLOW_DB_OPERATION: $Proxy194.findUnverifiedUsersInTimeRange took 810ms
2025-07-23 21:18:29.658 [ForkJoinPool.commonPool-worker-26] WARN  PERFORMANCE - 
                [] [588ms] [] SLOW_DB_OPERATION: $Proxy209.countUsersWithImprovement took 588ms
2025-07-23 21:18:29.819 [ForkJoinPool.commonPool-worker-27] WARN  PERFORMANCE - 
                [] [793ms] [] SLOW_DB_OPERATION: $Proxy209.countUsersWithImprovement took 793ms
2025-07-23 21:18:29.833 [MessageBroker-14] WARN  PERFORMANCE - 
                [generateDailyNotifications] [877ms] [] SLOW_DB_OPERATION: $Proxy207.findAllDueCardsForUser took 877ms
2025-07-23 21:18:30.115 [MessageBroker-8] WARN  PERFORMANCE - 
                [updateExpiredSubscriptions] [13223ms] [] SLOW_DB_OPERATION: $Proxy194.findBySubscriptionStatusAndSubscriptionEndDateBefore took 13223ms
2025-07-23 21:18:30.115 [MessageBroker-8] WARN  PERFORMANCE - 
                [updateExpiredSubscriptions] [13223ms] [] SLOW_SERVICE_METHOD: SubscriptionStatusService.updateExpiredSubscriptions took 13223ms
2025-07-23 21:18:33.607 [ForkJoinPool.commonPool-worker-28] WARN  PERFORMANCE - 
                [] [4522ms] [] SLOW_DB_OPERATION: $Proxy209.countUsersWithImprovement took 4522ms
2025-07-23 21:18:33.620 [ForkJoinPool.commonPool-worker-29] WARN  PERFORMANCE - 
                [] [3966ms] [] SLOW_DB_OPERATION: $Proxy209.getTotalCardsStudied took 3966ms
2025-07-23 21:18:33.622 [ForkJoinPool.commonPool-worker-27] WARN  PERFORMANCE - 
                [] [3803ms] [] SLOW_DB_OPERATION: $Proxy209.getAverageSessionsPerWeek took 3803ms
2025-07-23 21:18:33.628 [ForkJoinPool.commonPool-worker-26] WARN  PERFORMANCE - 
                [] [3969ms] [] SLOW_DB_OPERATION: $Proxy209.getAverageSessionsPerWeek took 3969ms
2025-07-23 21:18:34.651 [ForkJoinPool.commonPool-worker-30] WARN  PERFORMANCE - 
                [] [4649ms] [] SLOW_DB_OPERATION: $Proxy209.getTotalCardsStudied took 4649ms
2025-07-23 21:18:38.328 [MessageBroker-8] WARN  PERFORMANCE - 
                [updateExpiredSubscriptions] [31276ms] [] SLOW_SERVICE_METHOD: SubscriptionSchedulerService.updateExpiredSubscriptions took 31276ms
2025-07-23 21:18:38.337 [MessageBroker-4] WARN  PERFORMANCE - 
                [sendVerificationReminders] [4758ms] [] SLOW_DB_OPERATION: $Proxy194.findUnverifiedUsersInTimeRange took 4758ms
2025-07-23 21:18:38.353 [ForkJoinPool.commonPool-worker-28] WARN  PERFORMANCE - 
                [] [4744ms] [] SLOW_DB_OPERATION: $Proxy209.getAverageSessionsPerWeek took 4744ms
2025-07-23 21:18:38.363 [MessageBroker-4] WARN  PERFORMANCE - 
                [sendVerificationReminders] [22057ms] [] SLOW_SERVICE_METHOD: EmailVerificationReminderService.sendVerificationReminders took 22057ms
2025-07-23 21:18:38.383 [ForkJoinPool.commonPool-worker-30] WARN  PERFORMANCE - 
                [] [3732ms] [] SLOW_DB_OPERATION: $Proxy209.getAverageStudyStreakDays took 3732ms
2025-07-23 21:18:38.385 [ForkJoinPool.commonPool-worker-29] WARN  PERFORMANCE - 
                [] [4765ms] [] SLOW_DB_OPERATION: $Proxy209.getAverageStudyStreakDays took 4765ms
2025-07-23 21:18:38.409 [MessageBroker-14] ERROR PERFORMANCE - 
                [generateDailyNotifications] [8495ms] [] FAILED_DB_OPERATION: $Proxy209.getDistinctStudyDatesByUser failed after 8495ms with error: ConversionFailedException
2025-07-23 21:18:38.513 [StudyCards-Async-3] WARN  PERFORMANCE - 
                [createDueCardsReminder] [8428ms] [] SLOW_DB_OPERATION: $Proxy220.save took 8428ms
2025-07-23 21:18:38.513 [StudyCards-Async-3] WARN  PERFORMANCE - 
                [createDueCardsReminder] [8664ms] [] SLOW_SERVICE_METHOD: NotificationService.createDueCardsReminder took 8664ms
2025-07-23 21:18:38.839 [ForkJoinPool.commonPool-worker-31] INFO  PERFORMANCE - 
                [] [438ms] [] DB_OPERATION: $Proxy209.getTotalStudyHours took 438ms
2025-07-23 21:18:38.855 [ForkJoinPool.commonPool-worker-30] INFO  PERFORMANCE - 
                [] [448ms] [] DB_OPERATION: $Proxy209.getAverageAccuracyForPeriod took 448ms
2025-07-23 21:18:38.858 [ForkJoinPool.commonPool-worker-29] INFO  PERFORMANCE - 
                [] [449ms] [] DB_OPERATION: $Proxy209.getAverageAccuracyForPeriod took 449ms
2025-07-23 21:18:38.876 [ForkJoinPool.commonPool-worker-27] INFO  PERFORMANCE - 
                [] [467ms] [] DB_OPERATION: $Proxy207.countByDeckDeletedFalse took 467ms
2025-07-23 21:18:38.880 [ForkJoinPool.commonPool-worker-26] INFO  PERFORMANCE - 
                [] [487ms] [] DB_OPERATION: $Proxy207.countByDeckDeletedFalse took 487ms
2025-07-23 21:18:38.912 [MessageBroker-8] INFO  PERFORMANCE - 
                [updateExpiredSubscriptions] [584ms] [] SERVICE_METHOD: SubscriptionSchedulerService.updateExpiredSubscriptions took 584ms
2025-07-23 21:18:39.017 [MessageBroker-4] INFO  PERFORMANCE - 
                [sendVerificationReminders] [624ms] [] SERVICE_METHOD: EmailVerificationReminderService.sendVerificationReminders took 624ms
2025-07-23 21:18:39.023 [MessageBroker-16] WARN  PERFORMANCE - 
                [cacheWarming] [29054ms] [] SLOW_SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 29054ms
2025-07-23 21:18:39.027 [MessageBroker-10] WARN  PERFORMANCE - 
                [periodicHealthCheck] [31964ms] [] SLOW_SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 31964ms
2025-07-23 21:18:39.038 [MessageBroker-8] INFO  PERFORMANCE - 
                [updateExpiredSubscriptions] [105ms] [] DB_OPERATION: $Proxy194.findBySubscriptionStatusAndSubscriptionEndDateBefore took 105ms
2025-07-23 21:18:39.102 [MessageBroker-10] WARN  PERFORMANCE - 
                [periodicHealthCheck] [32056ms] [] SLOW_SERVICE_METHOD: StatisticsScheduledMonitor.periodicHealthCheck took 32056ms
2025-07-23 21:18:39.102 [MessageBroker-14] INFO  PERFORMANCE - 
                [generateDailyNotifications] [169ms] [] DB_OPERATION: $Proxy209.getDistinctStudyDatesByUser took 169ms
2025-07-23 21:18:39.104 [MessageBroker-16] WARN  PERFORMANCE - 
                [cacheWarming] [32054ms] [] SLOW_SERVICE_METHOD: StatisticsScheduledMonitor.cacheWarming took 32054ms
2025-07-23 21:18:39.106 [MessageBroker-14] WARN  PERFORMANCE - 
                [generateDailyNotifications] [29440ms] [] SLOW_SERVICE_METHOD: DashboardService.generateDailyNotifications took 29440ms
2025-07-23 21:18:39.122 [MessageBroker-12] WARN  PERFORMANCE - 
                [dailyStatisticsSummary] [23884ms] [] SLOW_SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 23884ms
2025-07-23 21:18:39.129 [MessageBroker-12] WARN  PERFORMANCE - 
                [dailyStatisticsSummary] [32075ms] [] SLOW_SERVICE_METHOD: StatisticsScheduledMonitor.dailyStatisticsSummary took 32075ms
2025-07-23 21:18:40.259 [MessageBroker-9] INFO  PERFORMANCE - 
                [periodicHealthCheck] [910ms] [] SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 910ms
2025-07-23 21:18:40.265 [ForkJoinPool.commonPool-worker-31] WARN  PERFORMANCE - 
                [] [911ms] [] SLOW_DB_OPERATION: $Proxy194.countUsersWithStudyDataSince took 911ms
2025-07-23 21:18:40.266 [MessageBroker-9] INFO  PERFORMANCE - 
                [periodicHealthCheck] [923ms] [] SERVICE_METHOD: StatisticsScheduledMonitor.periodicHealthCheck took 923ms
2025-07-23 21:18:40.272 [MessageBroker-8] WARN  PERFORMANCE - 
                [updateExpiredSubscriptions] [923ms] [] SLOW_DB_OPERATION: $Proxy194.findBySubscriptionStatusAndSubscriptionEndDateBefore took 923ms
2025-07-23 21:18:40.272 [MessageBroker-8] INFO  PERFORMANCE - 
                [updateExpiredSubscriptions] [923ms] [] SERVICE_METHOD: SubscriptionStatusService.updateExpiredTrials took 923ms
2025-07-23 21:18:40.340 [MessageBroker-16] INFO  PERFORMANCE - 
                [cacheWarming] [1000ms] [] SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 1000ms
2025-07-23 21:18:40.348 [MessageBroker-16] WARN  PERFORMANCE - 
                [cacheWarming] [1015ms] [] SLOW_SERVICE_METHOD: StatisticsScheduledMonitor.cacheWarming took 1015ms
2025-07-23 21:18:40.370 [MessageBroker-8] WARN  PERFORMANCE - 
                [updateExpiredSubscriptions] [1024ms] [] SLOW_SERVICE_METHOD: SubscriptionSchedulerService.updateExpiredSubscriptions took 1024ms
2025-07-23 21:18:41.420 [ForkJoinPool.commonPool-worker-26] INFO  PERFORMANCE - 
                [] [139ms] [] DB_OPERATION: $Proxy209.countUsersWithImprovement took 139ms
2025-07-23 21:18:47.393 [StudyCards-Async-5] ERROR PERFORMANCE - 
                [trackSearch] [0ms] [] FAILED_SERVICE_METHOD: UserService.getCurrentUser failed after 0ms with error: NullPointerException
2025-07-23 21:18:47.509 [http-nio-8082-exec-10] INFO  PERFORMANCE - 
                [searchDecks] [116ms] [] DB_OPERATION: $Proxy206.unifiedAdvancedSearch took 116ms
2025-07-23 23:03:01.282 [ForkJoinPool.commonPool-worker-52] INFO  PERFORMANCE - 
                [] [185ms] [] DB_OPERATION: $Proxy194.countActiveSubscriptionUsers took 185ms
2025-07-23 23:03:01.700 [MessageBroker-2] INFO  PERFORMANCE - 
                [periodicHealthCheck] [603ms] [] SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 603ms
2025-07-23 23:03:01.702 [MessageBroker-2] INFO  PERFORMANCE - 
                [periodicHealthCheck] [621ms] [] SERVICE_METHOD: StatisticsScheduledMonitor.periodicHealthCheck took 621ms
2025-07-23 23:52:48.263 [ForkJoinPool.commonPool-worker-61] INFO  PERFORMANCE - 
                [] [462ms] [] DB_OPERATION: $Proxy194.countActiveSubscriptionUsers took 462ms
2025-07-23 23:52:48.461 [ForkJoinPool.commonPool-worker-61] INFO  PERFORMANCE - 
                [] [197ms] [] DB_OPERATION: $Proxy194.countActiveUsersSince took 197ms
2025-07-23 23:52:48.826 [ForkJoinPool.commonPool-worker-61] INFO  PERFORMANCE - 
                [] [212ms] [] DB_OPERATION: $Proxy209.getTotalCardsStudied took 212ms
2025-07-23 23:52:49.175 [ForkJoinPool.commonPool-worker-61] INFO  PERFORMANCE - 
                [] [281ms] [] DB_OPERATION: $Proxy209.getAverageStudyStreakDays took 281ms
2025-07-23 23:52:49.404 [ForkJoinPool.commonPool-worker-61] INFO  PERFORMANCE - 
                [] [159ms] [] DB_OPERATION: $Proxy209.getAverageAccuracyForPeriod took 159ms
2025-07-23 23:52:50.081 [ForkJoinPool.commonPool-worker-61] WARN  PERFORMANCE - 
                [] [627ms] [] SLOW_DB_OPERATION: $Proxy206.countByPublicTrueAndDeletedFalse took 627ms
2025-07-23 23:52:50.238 [ForkJoinPool.commonPool-worker-61] INFO  PERFORMANCE - 
                [] [157ms] [] DB_OPERATION: $Proxy207.countByDeckDeletedFalse took 157ms
2025-07-23 23:52:50.447 [ForkJoinPool.commonPool-worker-61] INFO  PERFORMANCE - 
                [] [127ms] [] DB_OPERATION: $Proxy206.getAverageCardsPerDeck took 127ms
2025-07-23 23:52:50.449 [MessageBroker-4] WARN  PERFORMANCE - 
                [periodicHealthCheck] [2651ms] [] SLOW_SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 2651ms
2025-07-23 23:52:50.465 [MessageBroker-4] WARN  PERFORMANCE - 
                [periodicHealthCheck] [2780ms] [] SLOW_SERVICE_METHOD: StatisticsScheduledMonitor.periodicHealthCheck took 2780ms
2025-07-23 23:52:50.939 [ForkJoinPool.commonPool-worker-61] INFO  PERFORMANCE - 
                [] [159ms] [] DB_OPERATION: $Proxy209.getTotalCardsStudied took 159ms
2025-07-23 23:52:51.138 [ForkJoinPool.commonPool-worker-61] INFO  PERFORMANCE - 
                [] [105ms] [] DB_OPERATION: $Proxy209.getAverageAccuracyForPeriod took 105ms
2025-07-23 23:52:51.397 [ForkJoinPool.commonPool-worker-61] INFO  PERFORMANCE - 
                [] [115ms] [] DB_OPERATION: $Proxy207.countByDeckDeletedFalse took 115ms
2025-07-23 23:52:51.494 [MessageBroker-4] INFO  PERFORMANCE - 
                [periodicHealthCheck] [998ms] [] SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 998ms
2025-07-23 23:52:51.526 [MessageBroker-4] WARN  PERFORMANCE - 
                [periodicHealthCheck] [1061ms] [] SLOW_SERVICE_METHOD: StatisticsScheduledMonitor.periodicHealthCheck took 1061ms
