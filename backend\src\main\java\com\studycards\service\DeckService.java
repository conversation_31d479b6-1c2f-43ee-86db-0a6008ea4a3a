package com.studycards.service;

import com.studycards.dto.*;
import com.studycards.enums.CollaboratorPermission;
import com.studycards.enums.QuestionType;
import com.studycards.enums.SubscriptionStatus;
import com.studycards.event.DeckCreatedEvent;
import com.studycards.exception.BadRequestException;
import com.studycards.exception.ForbiddenException;
import com.studycards.exception.ResourceNotFoundException;
import com.studycards.exception.ServiceException;
import com.studycards.exception.ValidationException;
import com.studycards.exception.AuthenticationException;
import com.studycards.exception.SubscriptionRequiredException;
import com.studycards.exception.SubscriptionLimitException;
import com.studycards.exception.DeckCreationException;
import com.studycards.exception.DuplicateDeckException;
import com.studycards.exception.ConcurrentOperationException;
import com.studycards.model.*;
import com.studycards.repository.*;
import com.studycards.util.PaginationUtil;
import org.springframework.beans.factory.annotation.Autowired;
import java.math.BigDecimal;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.CacheManager;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.MessageSource;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.dao.DataAccessException;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Recover;
import org.springframework.retry.annotation.Retryable;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
@Transactional
@Service
@Slf4j
public class DeckService {

    @Autowired
    private DeckRepository deckRepository;

    @Autowired
    private CardRepository cardRepository;

    @Autowired
    private DeckTagRepository deckTagRepository;

    @Autowired
    private UserService userService;

    @Autowired
    @Lazy
    private CollaborationService collaborationService;

    @Autowired
    private StudySessionRepository studySessionRepository;

    @Autowired
    private DeckCardOperationService deckCardOperationService;

    @Autowired
    private SubscriptionLimitService subscriptionLimitService;

    @Autowired
    private PublicStatisticsService publicStatisticsService;

    @Autowired
    private DeckAuthorizationService deckAuthorizationService;

    @Autowired
    private RecommendationService recommendationService;

    @Autowired
    private RateLimitService rateLimitService;

    @Autowired
    private SubscriptionStatusService subscriptionStatusService;

    @Autowired
    private ContentVisibilityService contentVisibilityService;

    @Autowired
    private ApplicationEventPublisher applicationEventPublisher;

    @Autowired
    private CacheManager cacheManager;

    @Autowired
    private MessageSource messageSource;

    @Autowired
    private MeterRegistry meterRegistry;

    @Autowired(required = false)
    private RedissonClient redissonClient;

    @Autowired
    private AuditService auditService;

    @Autowired
    private CacheEvictionService cacheEvictionService;

    @Autowired
    private DeckRecommendationService deckRecommendationService;

    @Autowired
    private DeckValidationService deckValidationService;

    @Autowired
    private SecurityService securityService;

    @Autowired
    private UserStatisticsService userStatisticsService;

    // Configuration values
    @Value("${studycards.trial.daily-deck-limit:5}")
    private int TRIAL_DAILY_DECK_LIMIT;

    @Value("${studycards.deck.max-initial-cards:50}")
    private int MAX_INITIAL_CARDS;

    @Value("${studycards.deck.duplicate-check-enabled:true}")
    private boolean DUPLICATE_CHECK_ENABLED;

    /**
     * Enhanced deck creation with comprehensive validation and security
     * Replaces the basic createDeck method with production-ready implementation
     *
     * @param deckRequest The deck creation request
     * @return Enhanced deck creation response
     */
    @Transactional
    @Retryable(value = {DataAccessException.class}, maxAttempts = 3, backoff = @Backoff(delay = 1000))
    public DeckCreationResponse createDeckEnhanced(DeckRequest deckRequest) {
        // Add metrics and monitoring
        meterRegistry.counter("deck.creation.attempts").increment();
        Timer.Sample sample = Timer.start(meterRegistry);

        // Distributed lock for concurrent deck creation protection
        String lockKey = "deck_creation_" + getCurrentUserId();
        RLock lock = redissonClient != null ? redissonClient.getLock(lockKey) : null;

        try {
            // Acquire lock with timeout if Redis is available
            if (lock != null && !lock.tryLock(5, 30, TimeUnit.SECONDS)) {
                throw new ConcurrentOperationException("Another deck is being created. Please wait and try again.");
            }

            // Get and validate user
            User currentUser = userService.getCurrentUser();
            if (currentUser == null) {
                throw new AuthenticationException("User not authenticated");
            }

            // Comprehensive validation
            validateDeckCreationInput(deckRequest, currentUser);

            // Security and authorization checks
            validateDeckCreationSecurity(deckRequest, currentUser);

            // Business rule validation
            validateDeckCreationBusinessRules(deckRequest, currentUser);

            // Create and configure deck
            DeckCreationResponse response = createDeckWithMetadata(deckRequest, currentUser);

            meterRegistry.counter("deck.creation.success").increment();
            return response;

        } catch (ValidationException e) {
            meterRegistry.counter("deck.creation.validation_failed").increment();
            return DeckCreationResponse.validationError(e.getFieldErrors());
        } catch (DuplicateDeckException e) {
            meterRegistry.counter("deck.creation.duplicate").increment();
            return DeckCreationResponse.failure("A deck with this title already exists: " + e.getDeckTitle());
        } catch (SubscriptionLimitException e) {
            meterRegistry.counter("deck.creation.limit_exceeded").increment();
            return DeckCreationResponse.subscriptionLimit(e.getMessage(), null, null);
        } catch (Exception e) {
            meterRegistry.counter("deck.creation.failure").increment();
            log.error("Deck creation failed for user {}: {}", getCurrentUserId(), e.getMessage(), e);

            // Cleanup any partial state
            cleanupFailedDeckCreation(deckRequest);

            throw new DeckCreationException("Failed to create deck: " + e.getMessage(), e, "CREATE");
        } finally {
            if (lock != null && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
            sample.stop(Timer.builder("deck.creation.duration").register(meterRegistry));
        }
    }

    /**
     * Get a deck by ID, including deleted decks
     *
     * @param id The deck ID
     * @return The deck
     * @throws ResourceNotFoundException if the deck is not found
     */
    public Deck getDeckById(Long id) {
        return deckRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Deck not found with id: " + id));
    }

    /**
     * Get a non-deleted deck by ID
     *
     * @param id The deck ID
     * @return The deck
     * @throws ResourceNotFoundException if the deck is not found or is deleted
     */
    public Deck getNonDeletedDeckById(Long id) {
        Deck deck = getDeckById(id);
        if (deck.isDeleted()) {
            throw new ResourceNotFoundException("Deck not found with id: " + id);
        }
        return deck;
    }

    /**
     * Get a deck by ID with optimized loading of related entities
     *
     * @param id The deck ID
     * @return The deck with related entities loaded
     * @throws ResourceNotFoundException if the deck is not found
     */
    public Deck getDeckByIdWithDetails(Long id) {
        return deckRepository.findByIdWithDetails(id)
                .orElseThrow(() -> new ResourceNotFoundException("Deck not found with id: " + id));
    }

    public Page<DeckResponse> getPublicDecks(Pageable pageable) {
        Page<Deck> decks = deckRepository.findByIsPublicTrue(pageable);
        User currentUser = userService.getCurrentUser();

        // Filter out decks from creators without valid subscriptions
        List<DeckResponse> filteredDecks = decks.getContent().stream()
                .filter(deck -> {
                    boolean isVisible = contentVisibilityService.isContentVisible(deck.getCreator(), currentUser);
                    contentVisibilityService.logVisibilityDecision(deck.getCreator(), currentUser, isVisible, "public deck");
                    return isVisible;
                })
                .map(deck -> mapToDeckResponse(deck, currentUser))
                .collect(Collectors.toList());

        // FIXED: Use the original total count from the database, not the filtered size
        // This ensures pagination works correctly even when some items are filtered out
        return new org.springframework.data.domain.PageImpl<>(
                filteredDecks,
                pageable,
                decks.getTotalElements()
        );
    }

    /**
     * Basic search for public decks (legacy method)
     */
    public Page<DeckResponse> searchPublicDecks(String query, Pageable pageable) {
        Page<Deck> decks = deckRepository.searchPublicDecks(query, pageable);
        User currentUser = userService.getCurrentUser();

        // Filter out decks from creators without valid subscriptions
        List<DeckResponse> filteredDecks = decks.getContent().stream()
                .filter(deck -> {
                    boolean isVisible = contentVisibilityService.isContentVisible(deck.getCreator(), currentUser);
                    contentVisibilityService.logVisibilityDecision(deck.getCreator(), currentUser, isVisible, "search result");
                    return isVisible;
                })
                .map(deck -> mapToDeckResponse(deck, currentUser))
                .collect(Collectors.toList());

        // FIXED: Use the original total count from the database, not the filtered size
        return new org.springframework.data.domain.PageImpl<>(
                filteredDecks,
                pageable,
                decks.getTotalElements()
        );
    }

    /**
     * Advanced search with multiple criteria
     *
     * @param query Search text to find in title, description, or tags
     * @param isPublic Filter by public/private status (null for both)
     * @param creatorId Filter by creator ID (null for all creators)
     * @param tagName Filter by exact tag name (null for all tags)
     * @param pageable Pagination and sorting information
     * @return Page of deck responses matching the criteria
     */
    public Page<DeckResponse> advancedSearch(
            String query,
            Boolean isPublic,
            Long creatorId,
            String tagName,
            Pageable pageable) {

        Page<Deck> decks = deckRepository.advancedSearch(
                query,
                isPublic,
                creatorId,
                tagName,
                ensureValidDeckSorting(pageable));

        User currentUser = userService.getCurrentUser();

        // Filter out decks from creators without valid subscriptions
        // STRICT ENFORCEMENT: Even user's own decks are filtered if subscription expired
        List<Deck> filteredDecks = decks.getContent().stream()
                .filter(deck -> {
                    // Check if creator has valid subscription (applies to all decks including user's own)
                    boolean isVisible = contentVisibilityService.isContentVisible(deck.getCreator(), currentUser);
                    contentVisibilityService.logVisibilityDecision(deck.getCreator(), currentUser, isVisible, "advanced search");
                    return isVisible;
                })
                .collect(Collectors.toList());

        // Use batch mapping for better performance
        List<DeckResponse> mappedDecks = mapToDecksResponseBatch(filteredDecks, currentUser);

        return new org.springframework.data.domain.PageImpl<>(
                mappedDecks,
                pageable,
                mappedDecks.size()
        );
    }
    /**
     * Get popular decks with basic popularity algorithm (legacy method)
     * This method only considers the number of favorites
     * OPTIMIZED: Subscription filtering now handled at database level
     */
    @Cacheable(value = "popularDecks", key = "#pageable.pageNumber + '_' + #pageable.pageSize")
    public Page<DeckResponse> getPopularDecks(Pageable pageable) {
        Page<Deck> decks = deckRepository.findPopularPublicDecks(pageable);
        User currentUser = userService.getCurrentUser();

        // No need for additional filtering - subscription filtering is now handled in the repository
        // This significantly improves performance by avoiding N+1 queries
        return decks.map(deck -> mapToDeckResponse(deck, currentUser));
    }

    /**
     * Get popular decks with enhanced popularity algorithm
     * This method considers multiple factors:
     * - Number of favorites (highest weight)
     * - Number of recent study sessions
     * - Recency of the deck (newer decks get a boost)
     *
     * @param pageable Pagination information
     * @param timeFrame Time frame to consider for recent activity (e.g., "week", "month", "year")
     * @return Page of popular decks
     */
    @Cacheable(value = "trendingDecks", key = "#pageable.pageNumber + '_' + #pageable.pageSize + '_' + #timeFrame")
    public Page<DeckResponse> getEnhancedPopularDecks(Pageable pageable, String timeFrame) {
        LocalDateTime cutoffDate;

        // Determine the time frame cutoff date
        switch (timeFrame.toLowerCase()) {
            case "day":
                cutoffDate = LocalDateTime.now().minusDays(1);
                break;
            case "week":
                cutoffDate = LocalDateTime.now().minusWeeks(1);
                break;
            case "month":
                cutoffDate = LocalDateTime.now().minusMonths(1);
                break;
            case "year":
                cutoffDate = LocalDateTime.now().minusYears(1);
                break;
            default:
                // Default to 1 month
                cutoffDate = LocalDateTime.now().minusMonths(1);
        }

        Page<Deck> decks = deckRepository.findEnhancedPopularPublicDecks(cutoffDate, pageable);
        User currentUser = userService.getCurrentUser();

        // No need for additional filtering - subscription filtering is now handled in the repository
        // This significantly improves performance by avoiding N+1 queries
        return decks.map(deck -> mapToDeckResponse(deck, currentUser));
    }

    /**
     * Get user's decks with optional filtering
     * STRICT ENFORCEMENT: Users with expired subscriptions cannot access their own decks
     *
     * @param pageable Pagination and sorting information
     * @param parentId Optional parent folder ID to filter by
     * @param includeFolder Optional flag to include/exclude folders
     * @param isPublic Optional flag to filter by public/private status
     * @return Page of filtered deck responses
     */
    public Page<DeckResponse> getUserDecks(
            Pageable pageable,
            Long parentId,
            Boolean includeFolder,
            Boolean isPublic) {

        User currentUser = userService.getCurrentUser();

        // STRICT ENFORCEMENT: Block expired users from accessing their own content
        if (!contentVisibilityService.canAccessOwnContent(currentUser)) {
            // Return empty page for expired users
            return new org.springframework.data.domain.PageImpl<>(
                    Collections.emptyList(),
                    pageable,
                    0
            );
        }

        // Use the complex query with filters
        Page<Deck> decks = deckRepository.findByCreatorWithFilters(
                currentUser,
                parentId,
                includeFolder,
                isPublic,
                pageable);

        return decks.map(deck -> mapToDeckResponse(deck, currentUser));
    }

    /**
     * Legacy method for backward compatibility
     */
    public Page<DeckResponse> getUserDecks(Pageable pageable) {
        return getUserDecks(pageable, null, null, null);
    }

    public Page<DeckResponse> getUserFavoriteDecks(Pageable pageable) {
        User currentUser = userService.getCurrentUser();

        // STRICT ENFORCEMENT: Block expired users from accessing their own favorites
        if (!contentVisibilityService.canAccessOwnContent(currentUser)) {
            // Return empty page for expired users
            return new org.springframework.data.domain.PageImpl<>(
                    Collections.emptyList(),
                    pageable,
                    0
            );
        }

        // Use the custom repository method to get favorites with proper pagination
        Page<Deck> decks = deckRepository.findFavoritesByUserId(currentUser.getId(), pageable);

        return decks.map(deck -> mapToDeckResponse(deck, currentUser));
    }

    /**
     * Search within user's favorite decks
     *
     * @param query Search query
     * @param pageable Pagination information
     * @return Page of matching favorite decks
     */
    public Page<DeckResponse> searchUserFavorites(String query, Pageable pageable) {
        User currentUser = userService.getCurrentUser();

        // STRICT ENFORCEMENT: Block expired users from accessing their own favorites
        if (!contentVisibilityService.canAccessOwnContent(currentUser)) {
            return new org.springframework.data.domain.PageImpl<>(
                    Collections.emptyList(),
                    pageable,
                    0
            );
        }

        // Search within user's favorites
        Page<Deck> decks = deckRepository.searchUserFavorites(currentUser.getId(), query, pageable);

        return decks.map(deck -> mapToDeckResponse(deck, currentUser));
    }

    /**
     * Get personalized deck recommendations for the current user
     * This method combines:
     * 1. User's own decks that need review (due cards)
     * 2. Decks the user has studied recently but not completed
     * 3. Popular decks in categories the user has shown interest in
     * 4. New decks in categories the user has shown interest in
     * 5. Generally popular decks (as fallback)
     *
     * @param pageable Pagination information
     * @return Page of personalized deck recommendations
     */
    @Cacheable(value = "personalizedRecommendations", key = "#pageable.pageNumber + '_' + #pageable.pageSize + '_' + @userService.getCurrentUser().getId()", cacheManager = "shortLivedCacheManager")
    public Page<DeckResponse> getPersonalizedRecommendations(Pageable pageable) {
        // CRITICAL SECURITY FIX: Add input validation
        if (pageable == null) {
            throw new IllegalArgumentException("Pageable cannot be null");
        }

        // Validate pagination parameters
        if (pageable.getPageSize() > 100) {
            throw new IllegalArgumentException("Page size cannot exceed 100");
        }

        User currentUser = userService.getCurrentUser();

        // SECURITY FIX: Add rate limiting check
        rateLimitService.checkRecommendationLimit(currentUser.getId());

        // STRICT ENFORCEMENT: Block expired users from accessing personalized recommendations
        if (!contentVisibilityService.canAccessOwnContent(currentUser)) {
            log.info("Blocking expired user {} from accessing personalized recommendations", currentUser.getId());
            return new org.springframework.data.domain.PageImpl<>(
                    Collections.emptyList(),
                    pageable,
                    0
            );
        }

        // Get decks with due cards for the user (highest priority)
        List<Deck> decksWithDueCards = deckRepository.findDecksWithDueCardsForUser(
                currentUser.getId(),
                LocalDateTime.now().toLocalDate(),
                PageRequest.of(0, 5)
        );

        // Get decks the user has studied recently but not completed
        List<Deck> recentlyStudiedDecks = studySessionRepository.findRecentlyStudiedDecksForUser(
                currentUser.getId(),
                LocalDateTime.now().minusDays(14),
                PageRequest.of(0, 5)
        );

        // Get user's favorite tags
        List<String> userFavoriteTags = deckRepository.findUserFavoriteTags(
                currentUser.getId(),
                PageRequest.of(0, 5)
        );

        // Get popular decks in user's favorite categories
        List<Deck> popularInFavoriteCategories = new java.util.ArrayList<>();
        if (!userFavoriteTags.isEmpty()) {
            popularInFavoriteCategories = deckRepository.findPopularDecksByTags(
                    userFavoriteTags,
                    PageRequest.of(0, 5)
            );
        }

        // Get new decks in user's favorite categories
        List<Deck> newInFavoriteCategories = new java.util.ArrayList<>();
        if (!userFavoriteTags.isEmpty()) {
            // Use the DeckRepositoryCustom interface directly
            // The deckRepository implements DeckRepositoryCustom, so we can call the method directly
            newInFavoriteCategories = ((DeckRepositoryCustom) deckRepository).findNewDecksByTags(
                    userFavoriteTags,
                    LocalDateTime.now().minusDays(30),
                    PageRequest.of(0, 5)
            );
        }

        // Get generally popular decks as fallback
        // IMPORTANT: Don't add any additional sorting since the native query handles it
        List<Deck> generallyPopularDecks = deckRepository.findPopularPublicDecks(
                PageRequest.of(0, 10)
        ).getContent();

        // Combine all recommendations with priority order
        Set<Long> addedDeckIds = new java.util.HashSet<>();
        List<Deck> combinedRecommendations = new java.util.ArrayList<>();

        // Helper method to add decks avoiding duplicates
        java.util.function.Consumer<List<Deck>> addDecksNoDuplicates = decks -> {
            for (Deck deck : decks) {
                if (addedDeckIds.add(deck.getId())) {
                    combinedRecommendations.add(deck);
                }
            }
        };

        // Add recommendations in priority order
        addDecksNoDuplicates.accept(decksWithDueCards);
        addDecksNoDuplicates.accept(recentlyStudiedDecks);
        addDecksNoDuplicates.accept(popularInFavoriteCategories);
        addDecksNoDuplicates.accept(newInFavoriteCategories);
        addDecksNoDuplicates.accept(generallyPopularDecks);

        // Create a Page object from the combined list
        int start = (int) pageable.getOffset();
        int end = Math.min((start + pageable.getPageSize()), combinedRecommendations.size());

        List<Deck> pageContent = start < end
            ? combinedRecommendations.subList(start, end)
            : java.util.Collections.emptyList();

        Page<Deck> deckPage = new org.springframework.data.domain.PageImpl<>(
            pageContent,
            pageable,
            combinedRecommendations.size()
        );

        try {
            // PERFORMANCE FIX: Use batch mapping to eliminate N+1 queries
            List<DeckResponse> mappedDecks = mapToDecksResponseBatch(deckPage.getContent(), currentUser);

            return new org.springframework.data.domain.PageImpl<>(
                    mappedDecks,
                    pageable,
                    deckPage.getTotalElements()
            );

        } catch (Exception e) {
            log.error("Error generating personalized recommendations for user {}", currentUser.getId(), e);
            // CRITICAL: Fallback to basic popular decks on error
            try {
                return getPopularDecks(pageable);
            } catch (Exception fallbackError) {
                log.error("Fallback recommendation failed for user {}", currentUser.getId(), fallbackError);
                return new org.springframework.data.domain.PageImpl<>(
                        Collections.emptyList(),
                        pageable,
                        0
                );
            }
        }
    }

    /**
     * Create a new deck with optional initial cards and parent folder
     *
     * @param deckRequest The deck request containing deck information and optional initial cards
     * @return The created deck response
     */
    @Transactional
    public DeckResponse createDeck(DeckRequest deckRequest) {
        User currentUser = userService.getCurrentUser();

        // Check if the user has reached their deck limit
        subscriptionLimitService.validateCanCreateDeck();

        // Build the deck entity
        Deck deck = Deck.builder()
                .title(deckRequest.getTitle())
                .description(deckRequest.getDescription())
                .category(deckRequest.getCategory())
                .isPublic(deckRequest.getIsPublic())
                .creator(currentUser)
                .build();

        // Set parent folder if provided
        if (deckRequest.getParentFolderId() != null) {
            Deck parentFolder = getDeckById(deckRequest.getParentFolderId());

            // Verify parent is a folder
            if (!parentFolder.isFolder()) {
                throw new IllegalArgumentException("Parent must be a folder");
            }

            // Verify parent folder ownership
            if (!parentFolder.getCreator().getId().equals(currentUser.getId())) {
                throw new ForbiddenException("You do not have permission to create a deck in this folder");
            }

            deck.setParentDeck(parentFolder);
        }

        // Save the deck first (we need the ID for the tags)
        Deck savedDeck = deckRepository.save(deck);

        // Process tags (with validation)
        for (String tagName : deckRequest.getTags()) {
            // Validate tag name (alphanumeric with hyphens and spaces)
            if (!tagName.matches("^[a-zA-Z0-9\\-\\s]+$")) {
                throw new IllegalArgumentException("Tag names can only contain letters, numbers, hyphens, and spaces");
            }

            // Trim and lowercase the tag name
            String normalizedTagName = tagName.trim().toLowerCase();

            // Skip empty tags
            if (normalizedTagName.isEmpty()) {
                continue;
            }

            // Create a new DeckTag
            DeckTag deckTag = new DeckTag();
            deckTag.setDeck(savedDeck);
            deckTag.setTagName(normalizedTagName);
            deckTagRepository.save(deckTag);
        }

        // Refresh the deck to ensure we have the latest state
        savedDeck = deckRepository.findById(savedDeck.getId()).orElse(savedDeck);

        // Process initial cards if provided
        if (deckRequest.getInitialCards() != null && !deckRequest.getInitialCards().isEmpty()) {
            // Check if adding these cards would exceed the deck's card limit
            int newCardCount = deckRequest.getInitialCards().size();

            // Get the user's subscription plan
            if (currentUser.getSubscriptionPlan() != null && currentUser.getSubscriptionPlan().getMaxCardsPerDeck() != null) {
                int maxCards = currentUser.getSubscriptionPlan().getMaxCardsPerDeck();
                if (newCardCount > maxCards) {
                    throw new com.studycards.exception.SubscriptionLimitException(
                        "Adding " + newCardCount + " cards would exceed the maximum of " + maxCards +
                        " cards per deck allowed in your " + currentUser.getSubscriptionPlan().getName() +
                        " plan."
                    );
                }
            }
            for (DeckRequest.InitialCardRequest cardRequest : deckRequest.getInitialCards()) {
                // Detect question type for initial cards
                QuestionType questionType = QuestionType.detectFromContent(
                    cardRequest.getQuestion(), cardRequest.getAnswer());

                Card card = Card.builder()
                        .question(cardRequest.getQuestion())
                        .answer(cardRequest.getAnswer())
                        // Image URLs removed as per user requirement
                        .difficultyLevel(cardRequest.getDifficultyLevel())
                        .questionType(questionType)
                        .deck(savedDeck)
                        .easeFactor(new BigDecimal("2.5")) // Set default ease factor
                        .build();

                cardRepository.save(card);
            }
        }

        // Invalidate relevant caches
        // Evict public statistics cache since we've added a new deck
        publicStatisticsService.evictPublicStatisticsCache();

        return mapToDeckResponse(savedDeck, currentUser);
    }

    /**
     * Update a deck with optional batch card operations
     *
     * @param deckId The ID of the deck to update
     * @param deckRequest The deck request containing updated deck information and optional card operations
     * @return The updated deck response
     */
    /**
     * Manually evict the cache for a specific deck
     *
     * @param deckId The ID of the deck to evict from cache
     */
    @CacheEvict(value = {"enhancedDeckResponses"}, allEntries = true)
    public void evictDeckCache(Long deckId) {
        log.debug("Evicting cache for deck ID: {}", deckId);
        // This method doesn't need to do anything - the annotation handles the cache eviction
    }

    @Transactional
    @CacheEvict(value = "enhancedDeckResponses", key = "#deckId")
    public DeckResponse updateDeck(Long deckId, DeckRequest deckRequest) {
        // Input validation
        if (deckId == null) {
            throw new ValidationException("Deck ID cannot be null");
        }
        if (deckRequest == null) {
            throw new ValidationException("Deck request cannot be null");
        }

        User currentUser = userService.getCurrentUser();
        Deck deck = getDeckById(deckId);

        // Enhanced authorization checks
        if (!collaborationService.hasPermission(currentUser, deck, CollaboratorPermission.ADMIN)) {
            throw new ForbiddenException("You do not have permission to update this deck");
        }

        // Validate subscription for public deck updates
        if (deckRequest.getIsPublic() && !subscriptionLimitService.canCreatePublicDeck()) {
            throw new ForbiddenException("Active subscription required to make decks public");
        }

        // Validate and sanitize input
        validateDeckUpdateRequest(deckRequest);

        // Audit logging - capture original state
        String originalTitle = deck.getTitle();
        boolean originalPublicStatus = deck.isPublic();
        log.info("User {} updating deck {} (ID: {}). Original title: '{}', Original public: {}",
                currentUser.getUsername(), originalTitle, deckId, originalTitle, originalPublicStatus);

        // Update basic deck properties
        deck.setTitle(deckRequest.getTitle());
        deck.setDescription(deckRequest.getDescription());
        deck.setPublic(deckRequest.getIsPublic());

        // Update parent folder if specified
        if (deckRequest.getParentFolderId() != null) {
            if (deckRequest.getParentFolderId() == 0) {
                // Move to root (no parent)
                deck.setParentDeck(null);
            } else {
                // Move to specified folder
                Deck parentDeck = getDeckById(deckRequest.getParentFolderId());

                // Verify the parent is a folder
                if (!parentDeck.isFolder()) {
                    throw new BadRequestException("Parent deck must be a folder");
                }

                // Verify we're not creating a circular reference
                if (isCircularReference(deck.getId(), parentDeck.getId())) {
                    throw new BadRequestException("Cannot move a folder into one of its descendants");
                }

                deck.setParentDeck(parentDeck);
            }
        }

        // Save the updated deck first with error handling
        Deck updatedDeck;
        try {
            updatedDeck = deckRepository.save(deck);
        } catch (Exception e) {
            log.error("Failed to save deck update for deck ID: {}", deckId, e);
            throw new RuntimeException("Failed to update deck. Please try again.", e);
        }

        // Delete all existing tags for this deck with error handling
        try {
            deckTagRepository.deleteByDeck(updatedDeck);
        } catch (Exception e) {
            log.error("Failed to delete existing tags for deck ID: {}", deckId, e);
            // Continue execution as this is not critical
        }

        // Process tags (with validation)
        for (String tagName : deckRequest.getTags()) {
            // Validate tag name (alphanumeric with hyphens and spaces)
            if (!tagName.matches("^[a-zA-Z0-9\\-\\s]+$")) {
                throw new IllegalArgumentException("Tag names can only contain letters, numbers, hyphens, and spaces");
            }

            // Trim and lowercase the tag name
            String normalizedTagName = tagName.trim().toLowerCase();

            // Skip empty tags
            if (normalizedTagName.isEmpty()) {
                continue;
            }

            // Create a new DeckTag
            DeckTag deckTag = new DeckTag();
            deckTag.setDeck(updatedDeck);
            deckTag.setTagName(normalizedTagName);
            deckTagRepository.save(deckTag);
        }

        // Refresh the deck to ensure we have the latest state
        updatedDeck = deckRepository.findById(updatedDeck.getId()).orElse(updatedDeck);

        // Process batch card operations if provided with error handling
        if (deckRequest.getCardOperations() != null) {
            try {
                deckCardOperationService.processBatchCardOperations(updatedDeck, deckRequest.getCardOperations());
            } catch (Exception e) {
                log.error("Failed to process batch card operations for deck ID: {}", deckId, e);
                throw new RuntimeException("Failed to process card operations. Deck was updated but card changes failed.", e);
            }
        }

        // Invalidate relevant caches efficiently
        try {
            invalidateDeckCaches(updatedDeck, currentUser, deck.isPublic() != deckRequest.getIsPublic());
        } catch (Exception e) {
            log.warn("Failed to invalidate caches for deck ID: {}", deckId, e);
            // Continue execution as cache invalidation failure is not critical
        }

        // Final audit logging
        log.info("Successfully updated deck {} (ID: {}). New title: '{}', New public: {}, User: {}",
                updatedDeck.getTitle(), deckId, updatedDeck.getTitle(), updatedDeck.isPublic(), currentUser.getUsername());

        return mapToDeckResponse(updatedDeck, currentUser);
    }

    /**
     * Check if moving a folder would create a circular reference
     *
     * @param folderId The folder being moved
     * @param targetParentId The target parent folder
     * @return true if it would create a circular reference
     */
    private boolean isCircularReference(Long folderId, Long targetParentId) {
        if (folderId.equals(targetParentId)) {
            return true;
        }

        Deck targetParent = getDeckById(targetParentId);

        // Check if any ancestor of the target parent is the folder being moved
        while (targetParent.getParentDeck() != null) {
            if (targetParent.getParentDeck().getId().equals(folderId)) {
                return true;
            }
            targetParent = targetParent.getParentDeck();
        }

        return false;
    }

    /**
     * Soft delete a deck
     *
     * @param deckId The ID of the deck to delete
     */
    @Transactional
    @CacheEvict(value = {"enhancedDeckResponses"}, allEntries = true)
    public void deleteDeck(Long deckId) {
        User currentUser = userService.getCurrentUser();
        Deck deck = getDeckById(deckId);

        // Check if the user has ADMIN permission for the deck
        if (!collaborationService.hasPermission(currentUser, deck, CollaboratorPermission.ADMIN)) {
            throw new ForbiddenException("You do not have permission to delete this deck");
        }

        // If this is a folder, check if it has non-deleted child decks
        if (deck.isFolder() && !deck.getChildDecks().isEmpty()) {
            // Check if there are any non-deleted child decks
            boolean hasNonDeletedChildren = deck.getChildDecks().stream()
                    .anyMatch(child -> !child.isDeleted());

            if (hasNonDeletedChildren) {
                throw new BadRequestException("Cannot delete a folder that contains decks. Please move or delete the decks first.");
            }
        }

        // Save the original parent ID for potential restoration
        if (deck.getParentDeck() != null) {
            deck.setOriginalParentId(deck.getParentDeck().getId());
        }

        // Soft delete the deck
        deck.setDeleted(true);
        deck.setDeletedAt(LocalDateTime.now());

        // Remove from parent folder
        if (deck.getParentDeck() != null) {
            deck.setParentDeck(null);
        }

        deckRepository.save(deck);

        // Invalidate relevant caches
        // Evict public statistics cache if the deck was public
        if (deck.isPublic()) {
            publicStatisticsService.evictPublicStatisticsCache();
        }
    }

    /**
     * Permanently delete a deck
     *
     * @param deckId The ID of the deck to permanently delete
     */
    @Transactional
    public void permanentlyDeleteDeck(Long deckId) {
        User currentUser = userService.getCurrentUser();
        Deck deck = getDeckById(deckId);

        // Check if the user has ADMIN permission for the deck
        if (!collaborationService.hasPermission(currentUser, deck, CollaboratorPermission.ADMIN)) {
            throw new ForbiddenException("You do not have permission to delete this deck");
        }

        // Check if the deck is already soft-deleted
        if (!deck.isDeleted()) {
            throw new BadRequestException("Deck must be soft-deleted before permanent deletion");
        }

        deckRepository.delete(deck);
    }

    /**
     * Bulk permanently delete multiple decks
     *
     * @param deckIds List of deck IDs to permanently delete
     * @return Number of successfully deleted decks
     */
    @Transactional
    public int bulkPermanentlyDeleteDecks(List<Long> deckIds) {
        User currentUser = userService.getCurrentUser();
        int deletedCount = 0;
        List<String> errors = new ArrayList<>();

        for (Long deckId : deckIds) {
            try {
                Deck deck = getDeckById(deckId);

                // Check if the user has ADMIN permission for the deck
                if (!collaborationService.hasPermission(currentUser, deck, CollaboratorPermission.ADMIN)) {
                    errors.add("No permission to delete deck: " + deck.getTitle());
                    continue;
                }

                // Check if the deck is already soft-deleted
                if (!deck.isDeleted()) {
                    errors.add("Deck must be soft-deleted before permanent deletion: " + deck.getTitle());
                    continue;
                }

                deckRepository.delete(deck);
                deletedCount++;
            } catch (ResourceNotFoundException e) {
                errors.add("Deck not found: " + deckId);
            } catch (Exception e) {
                log.error("Error deleting deck {}: {}", deckId, e.getMessage());
                errors.add("Failed to delete deck: " + deckId);
            }
        }

        if (!errors.isEmpty()) {
            log.warn("Bulk deletion completed with {} errors: {}", errors.size(), String.join(", ", errors));
        }

        return deletedCount;
    }

    /**
     * Restore a deleted deck
     *
     * @param deckId The ID of the deck to restore
     * @return The restored deck
     */
    @Transactional
    @CacheEvict(value = {"enhancedDeckResponses"}, allEntries = true)
    public DeckResponse restoreDeck(Long deckId) {
        User currentUser = userService.getCurrentUser();
        Deck deck = getDeckById(deckId);

        // Check if the user has ADMIN permission for the deck
        if (!collaborationService.hasPermission(currentUser, deck, CollaboratorPermission.ADMIN)) {
            throw new ForbiddenException("You do not have permission to restore this deck");
        }

        // Check if the deck is soft-deleted
        if (!deck.isDeleted()) {
            throw new BadRequestException("Deck is not deleted");
        }

        // Restore the deck
        deck.setDeleted(false);
        deck.setDeletedAt(null);

        // Restore to original parent if it exists and is not deleted
        if (deck.getOriginalParentId() != null) {
            try {
                Deck parentDeck = getDeckById(deck.getOriginalParentId());
                if (!parentDeck.isDeleted()) {
                    deck.setParentDeck(parentDeck);
                }
            } catch (ResourceNotFoundException e) {
                // Parent no longer exists, leave as null
            }
        }

        deck.setOriginalParentId(null);
        Deck restoredDeck = deckRepository.save(deck);

        // Evict public statistics cache if the deck is public
        if (restoredDeck.isPublic()) {
            publicStatisticsService.evictPublicStatisticsCache();
        }

        return mapToDeckResponse(restoredDeck, currentUser);
    }

    /**
     * Bulk restore multiple deleted decks
     *
     * @param deckIds The list of deck IDs to restore
     * @return The number of successfully restored decks
     */
    @Transactional
    @CacheEvict(value = {"enhancedDeckResponses"}, allEntries = true)
    public int bulkRestoreDecks(List<Long> deckIds) {
        User currentUser = userService.getCurrentUser();
        int restoredCount = 0;
        List<String> errors = new ArrayList<>();

        for (Long deckId : deckIds) {
            try {
                Deck deck = getDeckById(deckId);

                // Check if the user has ADMIN permission for the deck
                if (!collaborationService.hasPermission(currentUser, deck, CollaboratorPermission.ADMIN)) {
                    errors.add("No permission to restore deck ID: " + deckId);
                    continue;
                }

                // Check if the deck is soft-deleted
                if (!deck.isDeleted()) {
                    errors.add("Deck ID " + deckId + " is not deleted");
                    continue;
                }

                // Restore the deck
                deck.setDeleted(false);
                deck.setDeletedAt(null);

                // Restore to original parent if it exists and is still available
                if (deck.getOriginalParentId() != null) {
                    try {
                        Deck originalParent = deckRepository.findById(deck.getOriginalParentId()).orElse(null);
                        if (originalParent != null && !originalParent.isDeleted() &&
                            collaborationService.hasPermission(currentUser, originalParent, CollaboratorPermission.VIEW)) {
                            deck.setParentDeck(originalParent);
                        }
                    } catch (Exception e) {
                        log.warn("Could not restore deck {} to original parent {}: {}",
                                deckId, deck.getOriginalParentId(), e.getMessage());
                    }
                    deck.setOriginalParentId(null);
                }

                deckRepository.save(deck);
                restoredCount++;

            } catch (Exception e) {
                log.error("Error restoring deck {}: {}", deckId, e.getMessage());
                errors.add("Failed to restore deck ID: " + deckId);
            }
        }

        if (!errors.isEmpty()) {
            log.warn("Bulk restore completed with {} errors: {}", errors.size(), errors);
        }

        // Evict public statistics cache if any restored deck is public
        publicStatisticsService.evictPublicStatisticsCache();

        return restoredCount;
    }

    /**
     * Get recently deleted decks for the current user
     *
     * @param pageable Pagination information
     * @return Page of recently deleted decks
     */
    public Page<DeckResponse> getDeletedDecks(Pageable pageable) {
        User currentUser = userService.getCurrentUser();
        Page<Deck> deletedDecks = deckRepository.findByCreatorAndDeletedTrueOrderByDeletedAtDesc(currentUser, pageable);
        return deletedDecks.map(deck -> mapToDeckResponse(deck, currentUser));
    }

    /**
     * Scheduled task to permanently delete decks that have been in trash for more than 30 days
     * Runs at 1:00 AM every day
     */
    @Scheduled(cron = "0 0 1 * * ?")
    @Transactional
    public void cleanupOldDeletedDecks() {
        log.info("Starting scheduled task to clean up old deleted decks");
        LocalDateTime threshold = LocalDateTime.now().minusDays(30);
        List<Deck> oldDeletedDecks = deckRepository.findByDeletedTrueAndDeletedAtBefore(threshold);

        int count = oldDeletedDecks.size();
        if (count > 0) {
            log.info("Found {} decks that have been in trash for more than 30 days - permanently deleting them", count);
            deckRepository.deleteAll(oldDeletedDecks);
            log.info("Successfully deleted {} old decks from trash", count);
        } else {
            log.info("No old deleted decks found to clean up");
        }
    }

    public DeckResponse mapToDeckResponse(Deck deck, User currentUser) {
        // CRITICAL SECURITY FIX: Add comprehensive input validation
        if (deck == null) {
            throw new IllegalArgumentException("Deck cannot be null");
        }
        if (currentUser == null) {
            throw new IllegalArgumentException("Current user cannot be null");
        }

        // CRITICAL SECURITY FIX: Add authorization check
        deckAuthorizationService.validateDeckAccess(currentUser, deck, "view");

        try {
            // PERFORMANCE FIX: Use optimized card count query (will be implemented in batch context)
            int cardCount = cardRepository.countByDeckId(deck.getId());

            // Use the cached method to check if the deck is favorited
            boolean isFavorite = userService.isDeckFavorited(deck.getId());

            // PERFORMANCE FIX: Optimize tag loading
            Set<String> tags = getOptimizedDeckTags(deck);

            return DeckResponse.builder()
                    .id(deck.getId())
                    .title(deck.getTitle())
                    .description(deck.getDescription())
                    .category(deck.getCategory())
                    .isPublic(deck.isPublic())
                    .creator(userService.mapToUserSummary(deck.getCreator()))
                    .cardCount(cardCount)
                    .tags(tags)
                    .createdAt(deck.getCreatedAt())
                    .updatedAt(deck.getUpdatedAt())
                    .favoriteCount(deckRepository.countFavoritesByDeckId(deck.getId()))
                    .isFavorite(isFavorite)
                    .isFolder(deck.isFolder())
                    .parentDeckId(deck.getParentDeck() != null ? deck.getParentDeck().getId() : null)
                    .deletedAt(deck.getDeletedAt())
                    .build();

        } catch (Exception e) {
            log.error("Error mapping deck {} to response for user {}", deck.getId(), currentUser.getId(), e);
            throw new ServiceException("Failed to map deck response", e);
        }
    }



    /**
     * PERFORMANCE FIX: Optimized tag loading
     */
    private Set<String> getOptimizedDeckTags(Deck deck) {
        Set<String> tags = new HashSet<>();

        try {
            if (deck.getDeckTags() != null && !deck.getDeckTags().isEmpty()) {
                tags = deck.getDeckTags().stream()
                        .map(DeckTag::getTagName)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toSet());
            } else {
                // Fallback: fetch from repository if not loaded
                List<DeckTag> deckTags = deckTagRepository.findByDeck(deck);
                if (deckTags != null && !deckTags.isEmpty()) {
                    tags = deckTags.stream()
                            .map(DeckTag::getTagName)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
                }
            }
        } catch (Exception e) {
            log.warn("Error loading tags for deck {}: {}", deck.getId(), e.getMessage());
            // Return empty set on error
        }

        return tags;
    }

    /**
     * PERFORMANCE FIX: Optimized deck mapping using batch context
     */
    public DeckResponse mapToDeckResponseOptimized(Deck deck, User currentUser, DeckMappingContext context) {
        // CRITICAL SECURITY FIX: Add comprehensive input validation
        if (deck == null) {
            throw new IllegalArgumentException("Deck cannot be null");
        }
        if (currentUser == null) {
            throw new IllegalArgumentException("Current user cannot be null");
        }
        if (context == null) {
            // Fallback to regular mapping if no context provided
            return mapToDeckResponse(deck, currentUser);
        }

        // CRITICAL SECURITY FIX: Add authorization check
        deckAuthorizationService.validateDeckAccess(currentUser, deck, "view");

        try {
            // PERFORMANCE FIX: Use batch-loaded data instead of individual queries
            int cardCount = context.getCardCount(deck.getId());
            Set<String> tags = context.getTags(deck.getId());
            int favoriteCount = context.getFavoriteCount(deck.getId());
            boolean isFavorite = context.isFavorite(deck.getId(), currentUser.getId());

            return DeckResponse.builder()
                    .id(deck.getId())
                    .title(deck.getTitle())
                    .description(deck.getDescription())
                    .category(deck.getCategory())
                    .isPublic(deck.isPublic())
                    .creator(userService.mapToUserSummary(deck.getCreator()))
                    .cardCount(cardCount)
                    .tags(tags)
                    .createdAt(deck.getCreatedAt())
                    .updatedAt(deck.getUpdatedAt())
                    .favoriteCount(favoriteCount)
                    .isFavorite(isFavorite)
                    .isFolder(deck.isFolder())
                    .parentDeckId(deck.getParentDeck() != null ? deck.getParentDeck().getId() : null)
                    .deletedAt(deck.getDeletedAt())
                    .build();

        } catch (Exception e) {
            log.error("Error mapping deck {} to response with context for user {}", deck.getId(), currentUser.getId(), e);
            throw new ServiceException("Failed to map deck response", e);
        }
    }

    /**
     * PERFORMANCE FIX: Batch mapping for multiple decks
     */
    public List<DeckResponse> mapToDecksResponseBatch(List<Deck> decks, User currentUser) {
        if (decks == null || decks.isEmpty()) {
            return new ArrayList<>();
        }

        // Create batch context for all decks
        DeckMappingContext context = DeckMappingContext.create(decks, currentUser, deckRepository);

        return decks.stream()
                .filter(Objects::nonNull)
                .map(deck -> {
                    try {
                        return mapToDeckResponseOptimized(deck, currentUser, context);
                    } catch (Exception e) {
                        log.error("Error mapping deck {} in batch for user {}", deck.getId(), currentUser.getId(), e);
                        return null; // Skip problematic decks
                    }
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * Get enhanced deck information including study statistics and related decks
     *
     * @param id The deck ID
     * @param includeCards Whether to include cards in the response
     * @return Enhanced deck response
     */
    @Cacheable(value = "enhancedDeckResponses", key = "#id + '_' + #includeCards")
    public EnhancedDeckResponse getEnhancedDeckById(Long id, boolean includeCards) {
        // Get the deck with optimized loading
        Deck deck = getDeckByIdWithDetails(id);
        User currentUser = userService.getCurrentUser();

        // Check if deck is deleted
        if (deck.isDeleted()) {
            throw new ResourceNotFoundException("Deck not found with id: " + id);
        }

        // Check permissions for private decks
        if (!deck.isPublic() && !deck.getCreator().getId().equals(currentUser.getId())) {
            // Check if user is a collaborator
            boolean isCollaborator = deck.getCollaborators().stream()
                    .anyMatch(c -> c.getUser().getId().equals(currentUser.getId()));

            if (!isCollaborator) {
                throw new ForbiddenException("You do not have permission to view this deck");
            }
        }

        // Build the basic response
        EnhancedDeckResponse response = new EnhancedDeckResponse();
        response.setId(deck.getId());
        response.setTitle(deck.getTitle());
        response.setDescription(deck.getDescription());
        response.setCategory(deck.getCategory());
        response.setPublic(deck.isPublic());
        response.setCreator(userService.mapToUserSummary(deck.getCreator()));
        response.setCardCount(cardRepository.countByDeckId(deck.getId()));

        // Ensure deckTags is initialized and log the tags
        Set<String> tags = new HashSet<>();
        if (deck.getDeckTags() != null && !deck.getDeckTags().isEmpty()) {
            tags = deck.getDeckTags().stream()
                    .map(DeckTag::getTagName)
                    .collect(Collectors.toSet());
            log.debug("Enhanced Deck ID: {} has {} tags: {}", deck.getId(), tags.size(), tags);
        } else {
            // If deckTags is null or empty, try to fetch them directly from the repository
            List<DeckTag> deckTags = deckTagRepository.findByDeck(deck);
            if (deckTags != null && !deckTags.isEmpty()) {
                tags = deckTags.stream()
                        .map(DeckTag::getTagName)
                        .collect(Collectors.toSet());
                log.debug("Fetched {} tags for Enhanced Deck ID: {} from repository: {}", tags.size(), deck.getId(), tags);
            } else {
                log.debug("No tags found for Enhanced Deck ID: {}", deck.getId());
            }
        }
        response.setTags(tags);

        response.setCreatedAt(deck.getCreatedAt());
        response.setUpdatedAt(deck.getUpdatedAt());
        // Use repository query to avoid LazyInitializationException
        response.setFavoriteCount(deckRepository.countFavoritesByDeckId(deck.getId()));
        // Use the cached method to check if the deck is favorited
        response.setFavorite(userService.isDeckFavorited(deck.getId()));
        response.setFolder(deck.isFolder());
        response.setParentDeckId(deck.getParentDeck() != null ? deck.getParentDeck().getId() : null);
        response.setDeletedAt(deck.getDeletedAt());

        // Add study statistics
        EnhancedDeckResponse.StudyStatistics studyStats = getStudyStatistics(deck, currentUser);
        response.setStudyStats(studyStats);

        // Add related decks
        List<Deck> relatedDecks = deckRepository.findRelatedDecksByTags(
                deck.getId(), PageRequest.of(0, 5));

        // Use batch mapping for better performance
        response.setRelatedDecks(mapToDecksResponseBatch(relatedDecks, currentUser));

        // Add cards if requested
        if (includeCards) {
            List<Card> cards = cardRepository.findByDeckId(deck.getId());
            response.setCards(cards.stream()
                    .map(this::mapToCardResponse)
                    .collect(Collectors.toList()));
        }

        return response;
    }

    /**
     * Get study statistics for a deck and user
     *
     * @param deck The deck
     * @param user The user
     * @return Study statistics
     */

    private EnhancedDeckResponse.StudyStatistics getStudyStatistics(Deck deck, User user) {
        // Get study statistics from repository
        Map<String, Object> stats = studySessionRepository.getDeckStudyStatistics(user, deck);

        // Count users who studied this deck
        Long studiedByCount = studySessionRepository.countUsersWhoStudiedDeck(deck);

        // Build the response
        return EnhancedDeckResponse.StudyStatistics.builder()
                .sessionCount(stats != null && stats.get("sessionCount") != null ?
                        ((Number) stats.get("sessionCount")).intValue() : 0)
                .totalCardsStudied(stats != null && stats.get("totalCardsStudied") != null ?
                        ((Number) stats.get("totalCardsStudied")).intValue() : 0)
                .totalCorrectAnswers(stats != null && stats.get("totalCorrectAnswers") != null ?
                        ((Number) stats.get("totalCorrectAnswers")).intValue() : 0)
                .averageAccuracy(stats != null && stats.get("averageAccuracy") != null ?
                        ((Number) stats.get("averageAccuracy")).doubleValue() : 0.0)
                .lastStudyTime(stats != null && stats.get("lastStudyTime") != null ?
                        (LocalDateTime) stats.get("lastStudyTime") : null)
                .studiedByCount(studiedByCount != null ? studiedByCount : 0L)
                .hasStudied(stats != null && stats.get("sessionCount") != null &&
                        ((Number) stats.get("sessionCount")).intValue() > 0)
                .build();
    }

    /**
     * Map a Card entity to a CardResponse DTO
     * This is a simplified version that only includes basic card information
     * For full card details, use the CardService.mapToCardResponse method
     *
     * @param card The card entity
     * @return CardResponse DTO with basic information
     */
    private CardResponse mapToCardResponse(Card card) {
        return CardResponse.builder()
                .id(card.getId())
                .question(card.getQuestion())
                .answer(card.getAnswer())
                // Image URLs removed as per user requirement
                .difficultyLevel(card.getDifficultyLevel())
                .questionType(card.getQuestionType())
                .createdAt(card.getCreatedAt())
                .updatedAt(card.getUpdatedAt())
                .deckId(card.getDeck().getId())
                .deckTitle(card.getDeck().getTitle())
                .learningProgress(card.getLearningProgress())
                .nextReviewDate(card.getNextReviewDate())
                .hint(card.getHint())
                .notes(card.getNotes())
                .reviewCount(card.getReviewCount())
                .correctCount(card.getCorrectCount())
                .intervalDays(card.getIntervalDays())
                .easeFactor(card.getEaseFactor().floatValue())
                .build();
    }
    /**
     * Get the user's deck hierarchy with caching
     *
     * @return List of deck responses with hierarchy information
     */
    // Temporarily disabled caching to fix method reference issue
    // @Cacheable(value = "userDeckHierarchy", key = "#root.target.getCurrentUser().getId()")
    public List<DeckResponse> getUserDeckHierarchy() {
        User currentUser = userService.getCurrentUser();
        List<Deck> rootDecks = deckRepository.findRootDecksByCreatorId(currentUser.getId());

        // Map to response DTOs with hierarchy info
        return rootDecks.stream()
                .map(deck -> mapToDeckResponseWithChildren(deck, currentUser))
                .collect(Collectors.toList());
    }

    /**
     * Get the user's deck hierarchy with pagination and metadata
     *
     * @param pageable Pagination information
     * @return DeckHierarchyResponse containing paginated items and metadata
     */
    public DeckHierarchyResponse getUserDeckHierarchyPaginated(Pageable pageable) {
        User currentUser = userService.getCurrentUser();

        // Get total counts for metadata
        List<Deck> allRootDecks = deckRepository.findRootDecksByCreatorId(currentUser.getId());
        int totalFolders = (int) allRootDecks.stream().filter(Deck::isFolder).count();
        int totalDecks = allRootDecks.size() - totalFolders;

        // Get paginated root decks
        Page<Deck> rootDecksPage = deckRepository.findRootDecksByCreatorIdPaginated(
                currentUser.getId(), pageable);

        // Map to response DTOs with hierarchy info
        List<DeckResponse> items = rootDecksPage.getContent().stream()
                .map(deck -> mapToDeckResponseWithChildren(deck, currentUser))
                .collect(Collectors.toList());

        // Calculate max depth
        int maxDepth = calculateMaxDepth(items);

        return DeckHierarchyResponse.builder()
                .items(items)
                .totalItems(Math.toIntExact(rootDecksPage.getTotalElements()))
                .totalFolders(totalFolders)
                .totalDecks(totalDecks)
                .maxDepth(maxDepth)
                .paginated(true)
                .page(pageable.getPageNumber())
                .size(pageable.getPageSize())
                .totalPages(rootDecksPage.getTotalPages())
                .build();
    }

    /**
     * Get child decks for a specific folder with caching
     *
     * @param folderId The folder ID
     * @return List of child deck responses
     */
    // Temporarily disabled caching to fix method reference issue
    // @Cacheable(value = "childDecks", key = "#folderId")
    public List<DeckResponse> getChildDecks(Long folderId) {
        User currentUser = userService.getCurrentUser();
        List<Deck> childDecks = deckRepository.findChildDecks(folderId);

        return childDecks.stream()
                .map(deck -> mapToDeckResponse(deck, currentUser))
                .collect(Collectors.toList());
    }

    /**
     * Calculate the maximum depth of a deck hierarchy
     *
     * @param decks List of deck responses
     * @return Maximum depth of the hierarchy
     */
    private int calculateMaxDepth(List<DeckResponse> decks) {
        int maxDepth = 1;

        for (DeckResponse deck : decks) {
            if (deck.getChildDecks() != null && !deck.getChildDecks().isEmpty()) {
                int childDepth = 1 + calculateMaxDepth(deck.getChildDecks());
                maxDepth = Math.max(maxDepth, childDepth);
            }
        }

        return maxDepth;
    }

    /**
     * Map a deck to a response DTO with its children
     *
     * @param deck The deck entity
     * @param currentUser The current user
     * @return Deck response with children
     */
    private DeckResponse mapToDeckResponseWithChildren(Deck deck, User currentUser) {
        DeckResponse response = mapToDeckResponse(deck, currentUser);

        // If this is a folder or has children, include children
        if (deck.isFolder() || !deck.getChildDecks().isEmpty()) {
            List<DeckResponse> children = deck.getChildDecks().stream()
                    .filter(child -> !child.isDeleted()) // Filter out deleted decks
                    .map(child -> mapToDeckResponseWithChildren(child, currentUser))
                    .collect(Collectors.toList());
            response.setChildDecks(children);
        }

        return response;
    }

    /**
     * Create a new folder
     *
     * @param folderName The name of the folder
     * @param parentDeckId The parent folder ID (optional)
     * @return The created folder response
     */
    @Transactional
    @CacheEvict(value = {"userDeckHierarchy", "childDecks"}, allEntries = true)
    public DeckResponse createFolder(String folderName, Long parentDeckId) {
        User currentUser = userService.getCurrentUser();

        // Check if the user has reached their deck limit (folders count as decks)
        subscriptionLimitService.validateCanCreateDeck();

        Deck folder = Deck.builder()
                .title(folderName)
                .isPublic(false)
                .isFolder(true)
                .creator(currentUser)
                .build();

        // Set parent if provided
        if (parentDeckId != null) {
            Deck parentDeck = getDeckById(parentDeckId);
            folder.setParentDeck(parentDeck);
        }

        Deck savedFolder = deckRepository.save(folder);
        return mapToDeckResponse(savedFolder, currentUser);
    }

    /**
     * Move a deck to a folder
     *
     * @param deckId The deck ID to move
     * @param folderDeckId The target folder ID (null for root)
     */
    @Transactional
    @CacheEvict(value = {"userDeckHierarchy", "childDecks"}, allEntries = true)
    public void moveDeckToFolder(Long deckId, Long folderDeckId) {
        User currentUser = userService.getCurrentUser();
        Deck deck = getDeckById(deckId);

        // Verify ownership
        if (!deck.getCreator().getId().equals(currentUser.getId())) {
            throw new ForbiddenException("You do not have permission to move this deck");
        }

        // Verify destination folder (or null for root)
        Deck folderDeck = null;
        if (folderDeckId != null) {
            folderDeck = getDeckById(folderDeckId);

            // Verify folder ownership
            if (!folderDeck.getCreator().getId().equals(currentUser.getId())) {
                throw new ForbiddenException("You do not have permission to move to this folder");
            }

            // Verify destination is a folder
            if (!folderDeck.isFolder()) {
                throw new IllegalArgumentException("Destination must be a folder");
            }

            // Verify we're not creating a circular reference if the deck being moved is a folder
            if (deck.isFolder() && isCircularReference(deck.getId(), folderDeckId)) {
                throw new BadRequestException("Cannot move a folder into one of its descendants");
            }
        }

        deck.setParentDeck(folderDeck);
        deckRepository.save(deck);
    }

    /**
     * Get current user ID for locking
     */
    private Long getCurrentUserId() {
        User currentUser = userService.getCurrentUser();
        return currentUser != null ? currentUser.getId() : null;
    }

    /**
     * Validate input parameters for deck creation
     *
     * @param deckRequest The deck creation request
     * @param user The user creating the deck
     */
    private void validateDeckCreationInput(DeckRequest deckRequest, User user) {
        // Use the comprehensive validation service
        deckValidationService.validateDeckCreation(deckRequest, user);
    }

    /**
     * Validate security and authorization for deck creation
     *
     * @param deckRequest The deck request
     * @param currentUser The current user
     */
    private void validateDeckCreationSecurity(DeckRequest deckRequest, User currentUser) {
        // Check subscription status
        if (!subscriptionLimitService.hasActiveSubscription(currentUser)) {
            throw new SubscriptionRequiredException("Active subscription required to create decks");
        }

        // Rate limiting check
        rateLimitService.checkDeckCreationLimit(currentUser.getId());

        // Security validation for all text content
        validateContentSecurity(deckRequest);
    }

    /**
     * Validate business rules for deck creation
     *
     * @param deckRequest The deck request
     * @param currentUser The current user
     */
    private void validateDeckCreationBusinessRules(DeckRequest deckRequest, User currentUser) {
        // Check deck creation limits
        subscriptionLimitService.validateCanCreateDeck();

        // MVP compliance checks
        validateMVPCompliance(currentUser, deckRequest);

        // Subscription-based restrictions
        validateSubscriptionRestrictions(currentUser, deckRequest);

        // Validate initial cards limit
        if (deckRequest.getInitialCards() != null && deckRequest.getInitialCards().size() > MAX_INITIAL_CARDS) {
            throw new ValidationException("initialCards",
                String.format("Cannot create more than %d initial cards at once", MAX_INITIAL_CARDS));
        }
    }

    /**
     * Validate content security
     *
     * @param deckRequest The deck request
     */
    private void validateContentSecurity(DeckRequest deckRequest) {
        // Check title for suspicious content
        if (deckRequest.getTitle() != null && securityService.containsSuspiciousContent(deckRequest.getTitle())) {
            throw new ValidationException("title", "Title contains inappropriate content");
        }

        // Check description for suspicious content
        if (deckRequest.getDescription() != null && securityService.containsSuspiciousContent(deckRequest.getDescription())) {
            throw new ValidationException("description", "Description contains inappropriate content");
        }

        // Check category for suspicious content
        if (deckRequest.getCategory() != null && securityService.containsSuspiciousContent(deckRequest.getCategory())) {
            throw new ValidationException("category", "Category contains inappropriate content");
        }

        // Check tags for suspicious content
        if (deckRequest.getTags() != null) {
            for (String tag : deckRequest.getTags()) {
                if (tag != null && securityService.containsSuspiciousContent(tag)) {
                    throw new ValidationException("tags", "One or more tags contain inappropriate content");
                }
            }
        }
    }

    /**
     * Validate MVP compliance
     *
     * @param user The user
     * @param deckRequest The deck request
     */
    private void validateMVPCompliance(User user, DeckRequest deckRequest) {
        SubscriptionStatus status = user.getSubscriptionStatus();

        // StudyCards MVP: Trial users have limited access
        if (status == SubscriptionStatus.TRIAL) {
            // Trial users can only create private decks
            if (Boolean.TRUE.equals(deckRequest.getIsPublic())) {
                throw new SubscriptionLimitException("Trial users can only create private decks");
            }

            // Check daily deck creation limit for trial users
            validateTrialUserDeckCreation(user);
        }

        // FREE status has same restrictions as EXPIRED per user requirements
        if (status == SubscriptionStatus.FREE) {
            throw new SubscriptionRequiredException("Active subscription required to create decks");
        }
    }

    /**
     * Validate trial user deck creation restrictions
     *
     * @param user The trial user
     */
    private void validateTrialUserDeckCreation(User user) {
        // Check daily deck creation limit for trial users
        long decksCreatedToday = deckRepository.countByCreatorAndCreatedAtAfter(
            user, LocalDateTime.now().toLocalDate().atStartOfDay());
        if (decksCreatedToday >= TRIAL_DAILY_DECK_LIMIT) {
            throw new SubscriptionLimitException(
                "Trial users limited to " + TRIAL_DAILY_DECK_LIMIT + " deck creations per day");
        }
    }

    /**
     * Validate subscription restrictions
     *
     * @param user The user
     * @param deckRequest The deck request
     */
    private void validateSubscriptionRestrictions(User user, DeckRequest deckRequest) {
        SubscriptionStatus status = user.getSubscriptionStatus();
        switch (status) {
            case EXPIRED:
            case CANCELLED:
                throw new SubscriptionRequiredException("Active subscription required to create decks");
            case FREE:
                throw new SubscriptionRequiredException("Active subscription required to create decks");
        }
    }

    /**
     * Create the actual deck with comprehensive setup and metadata
     *
     * @param deckRequest The deck request
     * @param currentUser The current user
     * @return DeckCreationResponse
     */
    private DeckCreationResponse createDeckWithMetadata(DeckRequest deckRequest, User currentUser) {
        try {
            // Build the deck entity with enhanced validation
            Deck deck = buildDeckEntity(deckRequest, currentUser);

            // Save the deck first (we need the ID for tags and cards)
            Deck savedDeck = deckRepository.save(deck);

            // Process tags with validation and normalization
            List<String> processedTags = processDeckTags(savedDeck, new ArrayList<>(deckRequest.getTags()));

            // Process initial cards if provided
            Integer initialCardCount = processInitialCards(savedDeck, deckRequest.getInitialCards(), currentUser);

            // Refresh the deck to ensure we have the latest state
            savedDeck = deckRepository.findById(savedDeck.getId()).orElse(savedDeck);

            // Publish domain event
            publishDeckCreatedEvent(savedDeck, currentUser, initialCardCount, processedTags);

            // Cache deck data for quick access
            cacheDeckData(savedDeck);

            // Audit logging
            auditService.logDeckCreation(savedDeck.getId(), currentUser.getId());

            // Invalidate relevant caches
            cacheEvictionService.evictDeckCaches(savedDeck.getId(), savedDeck.isPublic());
            publicStatisticsService.evictPublicStatisticsCache();

            // Build comprehensive response
            return buildDeckCreationResponse(savedDeck, currentUser, initialCardCount, processedTags);

        } catch (Exception e) {
            log.error("Failed to create deck for user {}: {}", currentUser.getId(), e.getMessage(), e);
            throw new DeckCreationException("Failed to create deck", e, "CREATE");
        }
    }

    /**
     * Build deck entity from request
     *
     * @param deckRequest The deck request
     * @param currentUser The current user
     * @return Built deck entity
     */
    private Deck buildDeckEntity(DeckRequest deckRequest, User currentUser) {
        Deck.DeckBuilder deckBuilder = Deck.builder()
                .title(deckRequest.getTitle().trim())
                .description(deckRequest.getDescription() != null ? deckRequest.getDescription().trim() : null)
                .category(deckRequest.getCategory() != null ? deckRequest.getCategory().trim() : null)
                .isPublic(Boolean.TRUE.equals(deckRequest.getIsPublic()))
                .creator(currentUser)
                .deleted(false);

        // Set parent folder if provided
        if (deckRequest.getParentFolderId() != null) {
            Deck parentFolder = getDeckById(deckRequest.getParentFolderId());
            deckBuilder.parentDeck(parentFolder);
        }

        return deckBuilder.build();
    }

    /**
     * Process deck tags with validation and normalization
     *
     * @param deck The saved deck
     * @param tags The tags to process
     * @return List of processed tag names
     */
    private List<String> processDeckTags(Deck deck, List<String> tags) {
        List<String> processedTags = new ArrayList<>();

        if (tags != null) {
            for (String tagName : tags) {
                if (tagName != null && !tagName.trim().isEmpty()) {
                    // Normalize tag name
                    String normalizedTagName = tagName.trim().toLowerCase();

                    // Skip duplicates
                    if (!processedTags.contains(normalizedTagName)) {
                        // Create and save DeckTag
                        DeckTag deckTag = new DeckTag();
                        deckTag.setDeck(deck);
                        deckTag.setTagName(normalizedTagName);
                        deckTagRepository.save(deckTag);

                        processedTags.add(normalizedTagName);
                    }
                }
            }
        }

        return processedTags;
    }

    /**
     * Process initial cards if provided
     *
     * @param deck The saved deck
     * @param initialCards The initial cards to create
     * @param currentUser The current user
     * @return Number of cards created
     */
    private Integer processInitialCards(Deck deck, List<DeckRequest.InitialCardRequest> initialCards, User currentUser) {
        if (initialCards == null || initialCards.isEmpty()) {
            return 0;
        }

        int cardCount = 0;
        for (DeckRequest.InitialCardRequest cardRequest : initialCards) {
            // Detect question type for initial cards
            QuestionType questionType = QuestionType.detectFromContent(
                cardRequest.getQuestion(), cardRequest.getAnswer());

            Card card = Card.builder()
                    .question(cardRequest.getQuestion().trim())
                    .answer(cardRequest.getAnswer().trim())
                    .difficultyLevel(cardRequest.getDifficultyLevel())
                    .questionType(questionType)
                    .deck(deck)
                    .deleted(false)
                    .easeFactor(new BigDecimal("2.5")) // Set default ease factor
                    .build();

            cardRepository.save(card);
            cardCount++;
        }

        return cardCount;
    }

    /**
     * Publish deck created domain event
     *
     * @param deck The created deck
     * @param user The user who created the deck
     * @param initialCardCount Number of initial cards
     * @param tags List of processed tags
     */
    private void publishDeckCreatedEvent(Deck deck, User user, Integer initialCardCount, List<String> tags) {
        try {
            DeckCreatedEvent event = DeckCreatedEvent.builder()
                .deckId(deck.getId())
                .userId(user.getId())
                .title(deck.getTitle())
                .category(deck.getCategory())
                .isPublic(deck.isPublic())
                .createdAt(deck.getCreatedAt())
                .initialCardCount(initialCardCount)
                .tags(tags)
                .parentFolderId(deck.getParentDeck() != null ? deck.getParentDeck().getId() : null)
                .subscriptionStatus(user.getSubscriptionStatus().name())
                .isFirstDeck(deckRepository.countByCreator(user) <= 1)
                .creationSource("web")
                .build();

            applicationEventPublisher.publishEvent(event);
        } catch (Exception e) {
            log.warn("Failed to publish deck created event: {}", e.getMessage());
            // Don't fail the main operation
        }
    }

    /**
     * Cache deck data for quick access
     *
     * @param deck The deck to cache
     */
    @Async
    private void cacheDeckData(Deck deck) {
        try {
            // This would be implemented with actual cache manager
            log.debug("Caching deck data for deck: {}", deck.getId());
        } catch (Exception e) {
            log.warn("Failed to cache deck data: {}", e.getMessage());
            // Don't fail the main operation
        }
    }

    /**
     * Build comprehensive deck creation response
     *
     * @param deck The created deck
     * @param user The user who created the deck
     * @param initialCardCount Number of initial cards created
     * @param tags List of processed tags
     * @return DeckCreationResponse
     */
    private DeckCreationResponse buildDeckCreationResponse(Deck deck, User user, Integer initialCardCount, List<String> tags) {
        // Calculate deck metadata
        DeckCreationResponse.DeckCreationMetadata metadata = calculateDeckMetadata(deck, user);

        // Generate recommendations
        List<String> recommendations = deckRecommendationService.generateDeckCreationRecommendations(deck, user);

        // Generate next steps
        List<String> nextSteps = deckRecommendationService.generateNextSteps(deck, user, initialCardCount);

        // Build parent folder info if applicable
        DeckCreationResponse.ParentFolderInfo parentFolderInfo = null;
        if (deck.getParentDeck() != null) {
            parentFolderInfo = DeckCreationResponse.ParentFolderInfo.builder()
                .folderId(deck.getParentDeck().getId())
                .folderName(deck.getParentDeck().getTitle())
                .folderPath(buildFolderPath(deck.getParentDeck()))
                .build();
        }

        return DeckCreationResponse.builder()
            .success(true)
            .deckId(deck.getId())
            .title(deck.getTitle())
            .description(deck.getDescription())
            .category(deck.getCategory())
            .isPublic(deck.isPublic())
            .createdAt(deck.getCreatedAt())
            .initialCardCount(initialCardCount)
            .tags(tags)
            .parentFolder(parentFolderInfo)
            .metadata(metadata)
            .recommendations(recommendations)
            .nextSteps(nextSteps)
            .deckUrl(buildDeckUrl(deck))
            .canShare(deck.isPublic())
            .message(messageSource.getMessage("deck.created.success",
                new Object[]{deck.getTitle()}, LocaleContextHolder.getLocale()))
            .build();
    }

    /**
     * Calculate deck creation metadata
     *
     * @param deck The created deck
     * @param user The user who created the deck
     * @return Deck creation metadata
     */
    private DeckCreationResponse.DeckCreationMetadata calculateDeckMetadata(Deck deck, User user) {
        // Get subscription plan information
        String subscriptionPlan = user.getSubscriptionStatus() != null ? user.getSubscriptionStatus().name() : "UNKNOWN";
        Integer maxCardsAllowed = user.getSubscriptionPlan() != null ? user.getSubscriptionPlan().getMaxCardsPerDeck() : null;

        // Calculate remaining decks allowed
        Integer remainingDecksAllowed = null;
        if (user.getSubscriptionPlan() != null && user.getSubscriptionPlan().getMaxDecks() != null) {
            long currentDeckCount = deckRepository.countByCreator(user);
            remainingDecksAllowed = Math.max(0, user.getSubscriptionPlan().getMaxDecks() - (int) currentDeckCount);
        }

        // Generate suggested tags
        List<String> suggestedTags = deckRecommendationService.generateSuggestedTags(deck.getTitle(), deck.getCategory());

        return DeckCreationResponse.DeckCreationMetadata.builder()
            .creatorUsername(user.getUsername())
            .estimatedStudyTime(calculateEstimatedStudyTime(deck))
            .difficultyLevel("Beginner") // Default for new decks
            .suggestedTags(suggestedTags)
            .maxCardsAllowed(maxCardsAllowed)
            .remainingDecksAllowed(remainingDecksAllowed)
            .subscriptionPlan(subscriptionPlan)
            .hasCollaborationFeatures(user.getSubscriptionStatus() == SubscriptionStatus.ACTIVE)
            .build();
    }

    /**
     * Calculate estimated study time for a deck
     *
     * @param deck The deck
     * @return Estimated study time in minutes
     */
    private Integer calculateEstimatedStudyTime(Deck deck) {
        // Basic calculation: 2 minutes per card
        long cardCount = cardRepository.countByDeckId(deck.getId());
        return (int) Math.max(5, cardCount * 2); // Minimum 5 minutes
    }

    /**
     * Build folder path for display
     *
     * @param folder The folder
     * @return Folder path string
     */
    private String buildFolderPath(Deck folder) {
        if (folder == null) {
            return "";
        }

        StringBuilder path = new StringBuilder();
        Deck current = folder;
        while (current != null) {
            if (path.length() > 0) {
                path.insert(0, " / ");
            }
            path.insert(0, current.getTitle());
            current = current.getParentDeck();
        }

        return path.toString();
    }

    /**
     * Build deck URL for sharing
     *
     * @param deck The deck
     * @return Deck URL
     */
    private String buildDeckUrl(Deck deck) {
        // This would be configured based on the application's base URL
        return "/app/decks/" + deck.getId();
    }

    /**
     * Cleanup any partial state after failed deck creation
     *
     * @param deckRequest The original request
     */
    private void cleanupFailedDeckCreation(DeckRequest deckRequest) {
        try {
            // Clean up any partially created deck data
            Long userId = getCurrentUserId();
            if (userId != null && deckRequest.getTitle() != null) {
                // Find and delete any incomplete decks with the same title
                Optional<Deck> incompleteDeck = deckRepository.findByCreatorIdAndTitleAndCardsIsEmpty(
                    userId, deckRequest.getTitle().trim());
                if (incompleteDeck.isPresent()) {
                    deckRepository.delete(incompleteDeck.get());
                    log.info("Cleaned up failed deck creation for user {} and title {}", userId, deckRequest.getTitle());
                }
            }
        } catch (Exception e) {
            log.warn("Failed to cleanup after deck creation failure: {}", e.getMessage());
        }
    }

    /**
     * Recovery method for retryable deck creation failures
     *
     * @param ex The exception that caused the failure
     * @param deckRequest The deck request
     * @return Error response
     */
    @Recover
    public DeckCreationResponse recoverFromDeckCreation(DataAccessException ex, DeckRequest deckRequest) {
        log.error("All retry attempts failed for deck creation: {}", ex.getMessage());
        return DeckCreationResponse.failure("Deck creation temporarily unavailable. Please try again later.");
    }

    /**
     * Validate deck update request for security and business rules
     *
     * @param deckRequest The deck request to validate
     * @throws ValidationException if validation fails
     */
    private void validateDeckUpdateRequest(DeckRequest deckRequest) {
        // Title validation
        if (deckRequest.getTitle() == null || deckRequest.getTitle().trim().isEmpty()) {
            throw new ValidationException("Deck title cannot be empty");
        }
        if (deckRequest.getTitle().length() > 255) {
            throw new ValidationException("Deck title cannot exceed 255 characters");
        }

        // Sanitize title for XSS prevention
        String sanitizedTitle = deckRequest.getTitle().trim()
            .replaceAll("<[^>]*>", "") // Remove HTML tags
            .replaceAll("[<>\"'&]", ""); // Remove dangerous characters
        deckRequest.setTitle(sanitizedTitle);

        // Description validation
        if (deckRequest.getDescription() != null) {
            if (deckRequest.getDescription().length() > 2000) {
                throw new ValidationException("Deck description cannot exceed 2000 characters");
            }

            // Sanitize description for XSS prevention
            String sanitizedDescription = deckRequest.getDescription().trim()
                .replaceAll("<script[^>]*>.*?</script>", "") // Remove script tags
                .replaceAll("javascript:", "") // Remove javascript: URLs
                .replaceAll("on\\w+\\s*=", ""); // Remove event handlers
            deckRequest.setDescription(sanitizedDescription);
        }

        // Category validation
        if (deckRequest.getCategory() != null && deckRequest.getCategory().length() > 100) {
            throw new ValidationException("Deck category cannot exceed 100 characters");
        }

        // Validate batch operations limits
        if (deckRequest.getCardOperations() != null) {
            int totalOperations = 0;
            if (deckRequest.getCardOperations().getCardsToCreate() != null) {
                totalOperations += deckRequest.getCardOperations().getCardsToCreate().size();
            }
            if (deckRequest.getCardOperations().getCardsToUpdate() != null) {
                totalOperations += deckRequest.getCardOperations().getCardsToUpdate().size();
            }
            if (deckRequest.getCardOperations().getCardsToDelete() != null) {
                totalOperations += deckRequest.getCardOperations().getCardsToDelete().size();
            }

            if (totalOperations > 100) {
                throw new ValidationException("Cannot process more than 100 card operations at once");
            }
        }
    }

    /**
     * Efficiently invalidate deck-related caches
     *
     * @param deck The updated deck
     * @param user The current user
     * @param publicStatusChanged Whether the public status changed
     */
    private void invalidateDeckCaches(Deck deck, User user, boolean publicStatusChanged) {
        // Invalidate specific deck cache
        cacheManager.getCache("enhancedDeckResponses").evict(deck.getId());

        // Invalidate user's deck list cache
        cacheManager.getCache("userDecks").evict(user.getId());

        // Invalidate deck cards cache
        cacheManager.getCache("deckCards").evict(deck.getId());

        // Only invalidate public statistics if public status changed
        if (publicStatusChanged) {
            publicStatisticsService.evictPublicStatisticsCache();
        }

        // Invalidate collaboration cache if deck has collaborators
        if (deck.getCollaborators() != null && !deck.getCollaborators().isEmpty()) {
            cacheManager.getCache("deckCollaborators").evict(deck.getId());
        }
    }

    /**
     * Ensures that the pageable has valid sorting for Deck entity fields.
     * Replaces invalid fields like 'popularity' with 'createdAt' and ensures DESC direction.
     */
    private Pageable ensureValidDeckSorting(Pageable pageable) {
        if (pageable.getSort().isUnsorted()) {
            // Default to createdAt DESC if no sorting specified
            return PageRequest.of(pageable.getPageNumber(), pageable.getPageSize(),
                    Sort.by(Sort.Direction.DESC, "createdAt"));
        }

        // Check if any sort property is invalid (like 'popularity')
        boolean hasInvalidSort = pageable.getSort().stream()
                .anyMatch(order -> "popularity".equals(order.getProperty()) ||
                                 !isValidDeckSortField(order.getProperty()));

        if (hasInvalidSort) {
            // Replace with default sorting
            return PageRequest.of(pageable.getPageNumber(), pageable.getPageSize(),
                    Sort.by(Sort.Direction.DESC, "createdAt"));
        }

        return pageable;
    }

    /**
     * Checks if a field name is a valid sort field for the Deck entity
     */
    private boolean isValidDeckSortField(String fieldName) {
        // Valid Deck entity fields that can be used for sorting
        return "id".equals(fieldName) ||
               "title".equals(fieldName) ||
               "description".equals(fieldName) ||
               "category".equals(fieldName) ||
               "isPublic".equals(fieldName) ||
               "createdAt".equals(fieldName) ||
               "updatedAt".equals(fieldName) ||
               "deleted".equals(fieldName) ||
               "deletedAt".equals(fieldName) ||
               "isFolder".equals(fieldName);
    }

}