2025-07-24 00:03:32.884 [MessageBroker-7] DEBUG c.s.s.StatisticsScheduledMonitor - 
                [periodicHealthCheck] [] [] [] [] Starting periodic statistics health check
2025-07-24 00:03:32.884 [MessageBroker-2] DEBUG org.hibernate.SQL - 
                [] [] [] [] [] 
    select
        eae1_0.id,
        eae1_0.additional_context,
        eae1_0.description,
        eae1_0.enum_type,
        eae1_0.event_timestamp,
        eae1_0.event_type,
        eae1_0.ip_address,
        eae1_0.new_value,
        eae1_0.old_value,
        eae1_0.request_id,
        eae1_0.security_threat_detected,
        eae1_0.session_id,
        eae1_0.severity_level,
        eae1_0.source_method,
        eae1_0.target_entity_id,
        eae1_0.target_entity_type,
        eae1_0.user_agent,
        eae1_0.user_email,
        eae1_0.user_id,
        eae1_0.username,
        eae1_0.validation_failed 
    from
        enum_audit_events eae1_0 
    where
        eae1_0.security_threat_detected=1 
        and eae1_0.event_timestamp>? 
    order by
        eae1_0.event_timestamp desc
2025-07-24 00:03:32.901 [MessageBroker-7] DEBUG c.s.s.StatisticsMonitoringService - 
                [periodicHealthCheck] [] [] [] [] Started statistics operation: getPublicStatistics
2025-07-24 00:03:32.901 [MessageBroker-7] INFO  c.s.service.PublicStatisticsService - 
                [periodicHealthCheck] [] [] [] [] Starting public statistics calculation
2025-07-24 00:03:32.901 [MessageBroker-7] DEBUG c.s.s.StatisticsMonitoringService - 
                [periodicHealthCheck] [] [] [] [] Started statistics operation: getUserStatistics
2025-07-24 00:03:32.901 [ForkJoinPool.commonPool-worker-63] DEBUG org.hibernate.SQL - 
                [] [] [] [] [] 
    select
        count_big(u1_0.id) 
    from
        users u1_0 
    where
        u1_0.subscription_status not in ('EXPIRED', 'CANCELLED')
2025-07-24 00:03:32.917 [MessageBroker-2] DEBUG org.hibernate.SQL - 
                [] [] [] [] [] 
    select
        eae1_0.ip_address,
        count_big(eae1_0.id) 
    from
        enum_audit_events eae1_0 
    where
        eae1_0.security_threat_detected=1 
        and eae1_0.event_timestamp>=? 
        and eae1_0.ip_address is not null 
    group by
        eae1_0.ip_address 
    having
        count_big(eae1_0.id)>=? 
    order by
        2 desc
2025-07-24 00:03:32.921 [ForkJoinPool.commonPool-worker-63] DEBUG org.hibernate.SQL - 
                [] [] [] [] [] 
    select
        count_big(distinct u1_0.id) 
    from
        users u1_0 
    join
        study_sessions ss1_0 
            on ss1_0.user_id=u1_0.id 
    where
        ss1_0.start_time>=? 
        and u1_0.subscription_status not in ('EXPIRED', 'CANCELLED')
2025-07-24 00:03:32.921 [MessageBroker-2] DEBUG org.hibernate.SQL - 
                [] [] [] [] [] 
    select
        eae1_0.user_id,
        eae1_0.username,
        count_big(eae1_0.id) 
    from
        enum_audit_events eae1_0 
    where
        eae1_0.validation_failed=1 
        and eae1_0.event_timestamp>=? 
        and eae1_0.user_id is not null 
    group by
        eae1_0.user_id,
        eae1_0.username 
    having
        count_big(eae1_0.id)>=? 
    order by
        3 desc
2025-07-24 00:03:32.930 [ForkJoinPool.commonPool-worker-63] DEBUG org.hibernate.SQL - 
                [] [] [] [] [] 
    select
        count_big(distinct u1_0.id) 
    from
        users u1_0 
    join
        study_sessions ss1_0 
            on ss1_0.user_id=u1_0.id 
    where
        ss1_0.start_time>=? 
        and ss1_0.cards_studied>0 
        and u1_0.subscription_status not in ('EXPIRED', 'CANCELLED')
2025-07-24 00:03:32.938 [ForkJoinPool.commonPool-worker-63] DEBUG org.hibernate.SQL - 
                [] [] [] [] [] 
    select
        count_big(distinct ss1_0.user_id) 
    from
        study_sessions ss1_0 
    join
        users u1_0 
            on u1_0.id=ss1_0.user_id 
    where
        ss1_0.start_time>=? 
        and ss1_0.cards_studied>0 
        and (
            (
                ss1_0.correct_answers*100.0
            )/ss1_0.cards_studied
        )>70.0 
        and u1_0.subscription_status not in ('EXPIRED', 'CANCELLED')
2025-07-24 00:03:32.946 [ForkJoinPool.commonPool-worker-63] DEBUG org.hibernate.SQL - 
                [] [] [] [] [] 
    SELECT
        CASE   
            WHEN COUNT(s.id) = 0 
                THEN 0   
            WHEN MIN(s.start_time) IS NULL 
                THEN 0   
            WHEN DATEDIFF(day, MIN(s.start_time), GETDATE()) < 7 
                THEN COUNT(s.id)   
            ELSE CAST(COUNT(s.id) * 7.0 / DATEDIFF(day, MIN(s.start_time), GETDATE()) AS INT) 
        END 
    FROM
        study_sessions s 
    JOIN
        users u 
            ON s.user_id = u.id 
    WHERE
        s.start_time >= DATEADD(month, -3, GETDATE()) 
        AND u.subscription_status NOT IN ('EXPIRED', 'CANCELLED')
2025-07-24 00:03:32.950 [MessageBroker-7] DEBUG c.s.s.StatisticsMonitoringService - 
                [periodicHealthCheck] [] [] [] [] Completed statistics operation: getUserStatistics in 49ms
2025-07-24 00:03:32.950 [MessageBroker-7] DEBUG c.s.s.StatisticsMonitoringService - 
                [periodicHealthCheck] [] [] [] [] Started statistics operation: getStudyStatistics
2025-07-24 00:03:32.951 [ForkJoinPool.commonPool-worker-63] DEBUG org.hibernate.SQL - 
                [] [] [] [] [] 
    select
        coalesce(sum(ss1_0.cards_studied), 0) 
    from
        study_sessions ss1_0 
    join
        users u1_0 
            on u1_0.id=ss1_0.user_id 
    where
        u1_0.subscription_status not in ('EXPIRED', 'CANCELLED')
2025-07-24 00:03:32.954 [ForkJoinPool.commonPool-worker-63] DEBUG org.hibernate.SQL - 
                [] [] [] [] [] 
    WITH UserLastStudyDates AS (  SELECT
        s.user_id, MAX(CAST(s.start_time AS DATE)) as last_study_date   
    FROM
        study_sessions s   
    JOIN
        users u 
            ON s.user_id = u.id   
    WHERE
        s.start_time >= DATEADD(month, -3, GETDATE())   
        AND u.subscription_status NOT IN ('EXPIRED', 'CANCELLED')   
    GROUP BY
        s.user_id ), ActiveUsers AS (  SELECT
        user_id, last_study_date,     DATEDIFF(day, last_study_date, GETDATE()) as days_since_last_study   
    FROM
        UserLastStudyDates   
    WHERE
        DATEDIFF(day, last_study_date, GETDATE()) <= 7 ), UserStreaks AS (  SELECT
        au.user_id,     CASE       
            WHEN au.days_since_last_study = 0 
                THEN 7       
            WHEN au.days_since_last_study = 1 
                THEN 5       
            WHEN au.days_since_last_study <= 3 
                THEN 3       
            ELSE 1     
        END as estimated_streak   
    FROM
        ActiveUsers au ) SELECT
        COALESCE(AVG(CAST(estimated_streak AS FLOAT)), 0) 
    FROM
        UserStreaks
2025-07-24 00:03:32.962 [ForkJoinPool.commonPool-worker-63] DEBUG org.hibernate.SQL - 
                [] [] [] [] [] 
    SELECT
        COALESCE(SUM(DATEDIFF(HOUR, s.start_time, s.end_time)), 0) 
    FROM
        study_sessions s 
    JOIN
        users u 
            ON s.user_id = u.id 
    WHERE
        s.end_time IS NOT NULL 
        AND u.subscription_status NOT IN ('EXPIRED', 'CANCELLED')
2025-07-24 00:03:32.968 [ForkJoinPool.commonPool-worker-63] DEBUG org.hibernate.SQL - 
                [] [] [] [] [] 
    select
        avg(((ss1_0.correct_answers*100.0)/ss1_0.cards_studied)) 
    from
        study_sessions ss1_0 
    join
        users u1_0 
            on u1_0.id=ss1_0.user_id 
    where
        ss1_0.start_time between ? and ? 
        and ss1_0.cards_studied>0 
        and u1_0.subscription_status not in ('EXPIRED', 'CANCELLED')
2025-07-24 00:03:32.974 [ForkJoinPool.commonPool-worker-63] DEBUG org.hibernate.SQL - 
                [] [] [] [] [] 
    select
        avg(((ss1_0.correct_answers*100.0)/ss1_0.cards_studied)) 
    from
        study_sessions ss1_0 
    join
        users u1_0 
            on u1_0.id=ss1_0.user_id 
    where
        ss1_0.start_time between ? and ? 
        and ss1_0.cards_studied>0 
        and u1_0.subscription_status not in ('EXPIRED', 'CANCELLED')
2025-07-24 00:03:32.974 [MessageBroker-7] DEBUG c.s.s.StatisticsMonitoringService - 
                [periodicHealthCheck] [] [] [] [] Completed statistics operation: getStudyStatistics in 24ms
2025-07-24 00:03:32.974 [MessageBroker-7] DEBUG c.s.s.StatisticsMonitoringService - 
                [periodicHealthCheck] [] [] [] [] Started statistics operation: getContentStatistics
2025-07-24 00:03:32.975 [ForkJoinPool.commonPool-worker-63] DEBUG org.hibernate.SQL - 
                [] [] [] [] [] 
    select
        count_big(d1_0.id) 
    from
        decks d1_0 
    join
        users c1_0 
            on c1_0.id=d1_0.creator_id 
    where
        d1_0.is_public=1 
        and d1_0.deleted=0 
        and c1_0.subscription_status not in ('EXPIRED', 'CANCELLED')
2025-07-24 00:03:32.983 [ForkJoinPool.commonPool-worker-63] DEBUG org.hibernate.SQL - 
                [] [] [] [] [] 
    select
        count_big(c1_0.id) 
    from
        cards c1_0 
    join
        decks d1_0 
            on d1_0.id=c1_0.deck_id 
    join
        users c2_0 
            on c2_0.id=d1_0.creator_id 
    where
        d1_0.deleted=0 
        and c2_0.subscription_status not in ('EXPIRED', 'CANCELLED')
2025-07-24 00:03:32.994 [ForkJoinPool.commonPool-worker-63] DEBUG org.hibernate.SQL - 
                [] [] [] [] [] 
    select
        count_big(distinct dt1_0.tag_name) 
    from
        deck_tags dt1_0 
    join
        decks d1_0 
            on d1_0.id=dt1_0.deck_id 
    join
        users c1_0 
            on c1_0.id=d1_0.creator_id 
    where
        c1_0.subscription_status not in ('EXPIRED', 'CANCELLED')
2025-07-24 00:03:33.001 [ForkJoinPool.commonPool-worker-63] DEBUG org.hibernate.SQL - 
                [] [] [] [] [] 
    SELECT
        COALESCE(AVG(CAST(card_count AS FLOAT)), 0) 
    FROM
        (SELECT
            COUNT(c.id) as card_count 
        FROM
            decks d 
        LEFT JOIN
            cards c 
                ON d.id = c.deck_id 
        JOIN
            users u 
                ON d.creator_id = u.id 
        WHERE
            d.deleted = 0 
            AND u.subscription_status NOT IN ('EXPIRED', 'CANCELLED') 
        GROUP BY
            d.id) as deck_card_counts
2025-07-24 00:03:33.008 [MessageBroker-7] DEBUG c.s.s.StatisticsMonitoringService - 
                [periodicHealthCheck] [] [] [] [] Completed statistics operation: getContentStatistics in 34ms
2025-07-24 00:03:33.008 [MessageBroker-7] INFO  c.s.service.PublicStatisticsService - 
                [periodicHealthCheck] [] [] [] [] Successfully calculated public statistics - Users: 3, Cards: 48, Decks: 1
2025-07-24 00:03:33.008 [MessageBroker-7] DEBUG c.s.s.StatisticsMonitoringService - 
                [periodicHealthCheck] [] [] [] [] Completed statistics operation: getPublicStatistics in 107ms
2025-07-24 00:03:33.008 [MessageBroker-7] DEBUG c.s.s.StatisticsScheduledMonitor - 
                [periodicHealthCheck] [] [] [] [] Periodic health check completed successfully in 124ms
2025-07-24 00:08:09.083 [SpringApplicationShutdownHook] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] [] Shutting down cache configuration
2025-07-24 00:08:09.083 [SpringApplicationShutdownHook] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] [] === Cache Statistics Report ===
2025-07-24 00:08:09.083 [SpringApplicationShutdownHook] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] [] Total cache operations: 3
2025-07-24 00:08:09.083 [SpringApplicationShutdownHook] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] [] Total cache errors: 0
2025-07-24 00:08:09.083 [SpringApplicationShutdownHook] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] [] Memory usage: ٨٫٦٥%
2025-07-24 00:08:09.083 [SpringApplicationShutdownHook] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] [] Cache semanticAnalysis: 0 hits, 0 misses, 0 evictions, {:.2f}% hit ratio
2025-07-24 00:08:09.083 [SpringApplicationShutdownHook] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] [] Cache enhancedNotifications: 0 hits, 0 misses, 0 evictions, {:.2f}% hit ratio
2025-07-24 00:08:09.083 [SpringApplicationShutdownHook] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] [] Cache semanticSimilarity: 0 hits, 0 misses, 0 evictions, {:.2f}% hit ratio
2025-07-24 00:08:09.083 [SpringApplicationShutdownHook] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] [] Cache unreadNotificationsCount: 0 hits, 0 misses, 0 evictions, {:.2f}% hit ratio
2025-07-24 00:08:09.083 [SpringApplicationShutdownHook] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] [] Cache conceptExtraction: 0 hits, 0 misses, 0 evictions, {:.2f}% hit ratio
2025-07-24 00:08:09.083 [SpringApplicationShutdownHook] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] [] Cache userFavorites: 0 hits, 0 misses, 0 evictions, {:.2f}% hit ratio
2025-07-24 00:08:09.083 [SpringApplicationShutdownHook] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] [] Cache longLived: 0 hits, 0 misses, 0 evictions, {:.2f}% hit ratio
2025-07-24 00:08:09.083 [SpringApplicationShutdownHook] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] [] Cache userNotifications: 0 hits, 0 misses, 0 evictions, {:.2f}% hit ratio
2025-07-24 00:08:09.083 [SpringApplicationShutdownHook] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] [] Cache decksByTag: 0 hits, 0 misses, 0 evictions, {:.2f}% hit ratio
2025-07-24 00:08:09.083 [SpringApplicationShutdownHook] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] [] Cache publicStatistics: 0 hits, 0 misses, 0 evictions, {:.2f}% hit ratio
2025-07-24 00:08:09.083 [SpringApplicationShutdownHook] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] [] Cache default: 0 hits, 0 misses, 2 evictions, {:.2f}% hit ratio
2025-07-24 00:08:09.083 [SpringApplicationShutdownHook] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] [] Cache userSubscriptionStatus: 0 hits, 0 misses, 0 evictions, {:.2f}% hit ratio
2025-07-24 00:08:09.083 [SpringApplicationShutdownHook] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] [] Cache enhancedDeckResponses: 0 hits, 0 misses, 0 evictions, {:.2f}% hit ratio
2025-07-24 00:08:09.083 [SpringApplicationShutdownHook] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] [] Cache captcha: 0 hits, 0 misses, 1 evictions, {:.2f}% hit ratio
2025-07-24 00:08:09.083 [SpringApplicationShutdownHook] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] [] Cache deckCards: 0 hits, 0 misses, 0 evictions, {:.2f}% hit ratio
2025-07-24 00:08:09.083 [SpringApplicationShutdownHook] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] [] Cache studyStreaks: 0 hits, 0 misses, 0 evictions, {:.2f}% hit ratio
2025-07-24 00:08:09.083 [SpringApplicationShutdownHook] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] [] Cache shortLived: 0 hits, 0 misses, 0 evictions, {:.2f}% hit ratio
2025-07-24 00:08:09.083 [SpringApplicationShutdownHook] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] [] Cache notificationPreferences: 0 hits, 0 misses, 0 evictions, {:.2f}% hit ratio
2025-07-24 00:08:09.083 [SpringApplicationShutdownHook] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] [] Cache tokenBlacklist: 0 hits, 0 misses, 0 evictions, {:.2f}% hit ratio
2025-07-24 00:08:09.083 [SpringApplicationShutdownHook] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] [] Cache userStatistics: 0 hits, 0 misses, 0 evictions, {:.2f}% hit ratio
2025-07-24 00:08:09.083 [SpringApplicationShutdownHook] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] [] Cache publicDecks: 0 hits, 0 misses, 0 evictions, {:.2f}% hit ratio
2025-07-24 00:08:09.083 [SpringApplicationShutdownHook] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] [] Cache trendingDecks: 0 hits, 0 misses, 0 evictions, {:.2f}% hit ratio
2025-07-24 00:08:09.083 [SpringApplicationShutdownHook] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] [] Cache newDecks: 0 hits, 0 misses, 0 evictions, {:.2f}% hit ratio
2025-07-24 00:08:09.083 [SpringApplicationShutdownHook] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] [] === End Cache Statistics ===
2025-07-24 00:08:09.083 [SpringApplicationShutdownHook] INFO  com.studycards.config.CacheConfig - 
                [] [] [] [] [] Cache configuration shutdown completed
2025-07-24 00:08:24.169 [SpringApplicationShutdownHook] INFO  c.s.config.RateLimitingFilter - 
                [] [] [] [] [] Shutting down RateLimitingFilter scheduler
2025-07-24 00:08:24.179 [SpringApplicationShutdownHook] INFO  c.s.config.RateLimitingFilter - 
                [] [] [] [] [] Shutting down RateLimitingFilter scheduler
2025-07-24 00:08:24.180 [SpringApplicationShutdownHook] INFO  c.s.config.RateLimitingFilter - 
                [] [] [] [] [] Shutting down RateLimitingFilter scheduler
