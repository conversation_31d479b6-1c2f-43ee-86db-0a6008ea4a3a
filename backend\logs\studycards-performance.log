2025-07-24 00:09:26.450 [MessageBroker-10] ERROR PERFORMANCE - 
                [hourlyPerformanceReport] [5ms] [] FAILED_SERVICE_METHOD: StatisticsMonitoringService.getMonitoringStats failed after 5ms with error: NullPointerException
2025-07-24 00:09:26.768 [MessageBroker-11] INFO  PERFORMANCE - 
                [sendVerificationReminders] [245ms] [] DB_OPERATION: $Proxy194.findUnverifiedUsersInTimeRange took 245ms
2025-07-24 00:09:26.768 [MessageBroker-7] INFO  PERFORMANCE - 
                [updateExpiredSubscriptions] [245ms] [] DB_OPERATION: $Proxy194.findBySubscriptionStatusAndSubscriptionEndDateBefore took 245ms
2025-07-24 00:09:26.769 [ForkJoinPool.commonPool-worker-1] INFO  PERFORMANCE - 
                [] [208ms] [] DB_OPERATION: $Proxy194.countActiveSubscriptionUsers took 208ms
2025-07-24 00:09:26.769 [ForkJoinPool.commonPool-worker-2] INFO  PERFORMANCE - 
                [] [209ms] [] DB_OPERATION: $Proxy194.countActiveSubscriptionUsers took 209ms
2025-07-24 00:09:26.776 [main] INFO  PERFORMANCE - 
                [] [119ms] [] DB_OPERATION: $Proxy211.count took 119ms
2025-07-24 00:09:27.064 [MessageBroker-8] INFO  PERFORMANCE - 
                [cacheWarming] [517ms] [] SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 517ms
2025-07-24 00:09:27.064 [MessageBroker-9] INFO  PERFORMANCE - 
                [periodicHealthCheck] [517ms] [] SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 517ms
2025-07-24 00:09:27.066 [MessageBroker-9] INFO  PERFORMANCE - 
                [periodicHealthCheck] [624ms] [] SERVICE_METHOD: StatisticsScheduledMonitor.periodicHealthCheck took 624ms
2025-07-24 00:09:27.066 [MessageBroker-8] INFO  PERFORMANCE - 
                [cacheWarming] [628ms] [] SERVICE_METHOD: StatisticsScheduledMonitor.cacheWarming took 628ms
