2025-07-24 00:09:26.450 [MessageBroker-10] ERROR PERFORMANCE - 
                [hourlyPerformanceReport] [5ms] [] FAILED_SERVICE_METHOD: StatisticsMonitoringService.getMonitoringStats failed after 5ms with error: NullPointerException
2025-07-24 00:09:26.768 [MessageBroker-11] INFO  PERFORMANCE - 
                [sendVerificationReminders] [245ms] [] DB_OPERATION: $Proxy194.findUnverifiedUsersInTimeRange took 245ms
2025-07-24 00:09:26.768 [MessageBroker-7] INFO  PERFORMANCE - 
                [updateExpiredSubscriptions] [245ms] [] DB_OPERATION: $Proxy194.findBySubscriptionStatusAndSubscriptionEndDateBefore took 245ms
2025-07-24 00:09:26.769 [ForkJoinPool.commonPool-worker-1] INFO  PERFORMANCE - 
                [] [208ms] [] DB_OPERATION: $Proxy194.countActiveSubscriptionUsers took 208ms
2025-07-24 00:09:26.769 [ForkJoinPool.commonPool-worker-2] INFO  PERFORMANCE - 
                [] [209ms] [] DB_OPERATION: $Proxy194.countActiveSubscriptionUsers took 209ms
2025-07-24 00:09:26.776 [main] INFO  PERFORMANCE - 
                [] [119ms] [] DB_OPERATION: $Proxy211.count took 119ms
2025-07-24 00:09:27.064 [MessageBroker-8] INFO  PERFORMANCE - 
                [cacheWarming] [517ms] [] SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 517ms
2025-07-24 00:09:27.064 [MessageBroker-9] INFO  PERFORMANCE - 
                [periodicHealthCheck] [517ms] [] SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 517ms
2025-07-24 00:09:27.066 [MessageBroker-9] INFO  PERFORMANCE - 
                [periodicHealthCheck] [624ms] [] SERVICE_METHOD: StatisticsScheduledMonitor.periodicHealthCheck took 624ms
2025-07-24 00:09:27.066 [MessageBroker-8] INFO  PERFORMANCE - 
                [cacheWarming] [628ms] [] SERVICE_METHOD: StatisticsScheduledMonitor.cacheWarming took 628ms
2025-07-24 00:39:29.853 [http-nio-8082-exec-8] INFO  PERFORMANCE - 
                [getStudyActivityData] [122ms] [] DB_OPERATION: $Proxy209.findByUserAndDateRange took 122ms
2025-07-24 00:39:29.866 [http-nio-8082-exec-1] INFO  PERFORMANCE - 
                [getUserStudySessions] [107ms] [] DB_OPERATION: $Proxy209.findByUserWithDeckFetch took 107ms
2025-07-24 00:39:29.888 [http-nio-8082-exec-2] INFO  PERFORMANCE - 
                [getUserDecks] [103ms] [] DB_OPERATION: $Proxy206.findByCreatorWithFilters took 103ms
2025-07-24 00:39:29.911 [http-nio-8082-exec-10] INFO  PERFORMANCE - 
                [getEnhancedPopularDecks] [110ms] [] DB_OPERATION: $Proxy206.findEnhancedPopularPublicDecks took 110ms
2025-07-24 00:39:30.039 [http-nio-8082-exec-5] ERROR PERFORMANCE - 
                [getDashboardData] [71ms] [] FAILED_DB_OPERATION: $Proxy209.getDistinctStudyDatesByUser failed after 71ms with error: ConversionFailedException
2025-07-24 00:39:30.297 [StudyCards-Async-2] INFO  PERFORMANCE - 
                [createDueCardsReminder] [142ms] [] DB_OPERATION: $Proxy220.save took 142ms
2025-07-24 00:40:03.206 [http-nio-8082-exec-10] ERROR PERFORMANCE - 
                [getDashboardData] [20ms] [] FAILED_DB_OPERATION: $Proxy209.getDistinctStudyDatesByUser failed after 20ms with error: ConversionFailedException
2025-07-24 00:41:00.455 [http-nio-8082-exec-9] ERROR PERFORMANCE - 
                [mapToDeckResponse] [15ms] [] FAILED_SERVICE_METHOD: DeckService.mapToDeckResponse failed after 15ms with error: ServiceException
2025-07-24 00:41:00.458 [http-nio-8082-exec-9] ERROR PERFORMANCE - 
                [] [37ms] [] FAILED_ENDPOINT: DeckController.getDeckById failed after 37ms with error: ServiceException
2025-07-24 00:41:40.788 [http-nio-8082-exec-10] ERROR PERFORMANCE - 
                [mapToDeckResponse] [11ms] [] FAILED_SERVICE_METHOD: DeckService.mapToDeckResponse failed after 11ms with error: ServiceException
2025-07-24 00:41:40.789 [http-nio-8082-exec-10] ERROR PERFORMANCE - 
                [] [24ms] [] FAILED_ENDPOINT: DeckController.getDeckById failed after 24ms with error: ServiceException
2025-07-24 00:41:59.793 [http-nio-8082-exec-6] ERROR PERFORMANCE - 
                [mapToDeckResponse] [11ms] [] FAILED_SERVICE_METHOD: DeckService.mapToDeckResponse failed after 11ms with error: ServiceException
2025-07-24 00:41:59.795 [http-nio-8082-exec-6] ERROR PERFORMANCE - 
                [] [21ms] [] FAILED_ENDPOINT: DeckController.getDeckById failed after 21ms with error: ServiceException
2025-07-24 00:42:26.795 [http-nio-8082-exec-9] ERROR PERFORMANCE - 
                [mapToDeckResponse] [17ms] [] FAILED_SERVICE_METHOD: DeckService.mapToDeckResponse failed after 17ms with error: ServiceException
2025-07-24 00:42:26.796 [http-nio-8082-exec-9] ERROR PERFORMANCE - 
                [] [31ms] [] FAILED_ENDPOINT: DeckController.getDeckById failed after 31ms with error: ServiceException
2025-07-24 00:44:00.186 [StudyCards-Async-4] ERROR PERFORMANCE - 
                [trackSearch] [1ms] [] FAILED_SERVICE_METHOD: UserService.getCurrentUser failed after 1ms with error: NullPointerException
2025-07-24 00:44:04.874 [StudyCards-Async-5] ERROR PERFORMANCE - 
                [trackSearch] [0ms] [] FAILED_SERVICE_METHOD: UserService.getCurrentUser failed after 0ms with error: NullPointerException
2025-07-24 00:44:08.581 [StudyCards-Async-1] ERROR PERFORMANCE - 
                [trackSearch] [1ms] [] FAILED_SERVICE_METHOD: UserService.getCurrentUser failed after 1ms with error: NullPointerException
2025-07-24 00:44:16.998 [StudyCards-Async-2] ERROR PERFORMANCE - 
                [trackSearch] [0ms] [] FAILED_SERVICE_METHOD: UserService.getCurrentUser failed after 0ms with error: NullPointerException
2025-07-24 00:44:18.854 [StudyCards-Async-3] ERROR PERFORMANCE - 
                [trackSearch] [0ms] [] FAILED_SERVICE_METHOD: UserService.getCurrentUser failed after 0ms with error: NullPointerException
2025-07-24 00:44:35.642 [StudyCards-Async-4] ERROR PERFORMANCE - 
                [trackSearch] [0ms] [] FAILED_SERVICE_METHOD: UserService.getCurrentUser failed after 0ms with error: NullPointerException
2025-07-24 00:44:35.679 [StudyCards-Async-5] ERROR PERFORMANCE - 
                [trackSearch] [0ms] [] FAILED_SERVICE_METHOD: UserService.getCurrentUser failed after 0ms with error: NullPointerException
2025-07-24 00:44:36.317 [StudyCards-Async-1] ERROR PERFORMANCE - 
                [trackSearch] [0ms] [] FAILED_SERVICE_METHOD: UserService.getCurrentUser failed after 0ms with error: NullPointerException
2025-07-24 00:44:36.451 [StudyCards-Async-2] ERROR PERFORMANCE - 
                [trackSearch] [0ms] [] FAILED_SERVICE_METHOD: UserService.getCurrentUser failed after 0ms with error: NullPointerException
2025-07-24 00:44:44.952 [StudyCards-Async-3] ERROR PERFORMANCE - 
                [trackSearch] [0ms] [] FAILED_SERVICE_METHOD: UserService.getCurrentUser failed after 0ms with error: NullPointerException
2025-07-24 00:44:53.502 [StudyCards-Async-4] ERROR PERFORMANCE - 
                [trackSearch] [0ms] [] FAILED_SERVICE_METHOD: UserService.getCurrentUser failed after 0ms with error: NullPointerException
2025-07-24 00:45:00.670 [StudyCards-Async-5] ERROR PERFORMANCE - 
                [trackSearch] [0ms] [] FAILED_SERVICE_METHOD: UserService.getCurrentUser failed after 0ms with error: NullPointerException
2025-07-24 00:45:18.377 [StudyCards-Async-1] ERROR PERFORMANCE - 
                [trackSearch] [0ms] [] FAILED_SERVICE_METHOD: UserService.getCurrentUser failed after 0ms with error: NullPointerException
2025-07-24 00:45:22.950 [StudyCards-Async-2] ERROR PERFORMANCE - 
                [trackSearch] [0ms] [] FAILED_SERVICE_METHOD: UserService.getCurrentUser failed after 0ms with error: NullPointerException
2025-07-24 00:45:23.558 [StudyCards-Async-3] ERROR PERFORMANCE - 
                [trackSearch] [0ms] [] FAILED_SERVICE_METHOD: UserService.getCurrentUser failed after 0ms with error: NullPointerException
2025-07-24 00:45:23.971 [StudyCards-Async-4] ERROR PERFORMANCE - 
                [trackSearch] [0ms] [] FAILED_SERVICE_METHOD: UserService.getCurrentUser failed after 0ms with error: NullPointerException
2025-07-24 00:45:26.006 [StudyCards-Async-5] ERROR PERFORMANCE - 
                [trackSearch] [2ms] [] FAILED_SERVICE_METHOD: UserService.getCurrentUser failed after 2ms with error: NullPointerException
2025-07-24 00:45:26.265 [StudyCards-Async-1] ERROR PERFORMANCE - 
                [trackSearch] [0ms] [] FAILED_SERVICE_METHOD: UserService.getCurrentUser failed after 0ms with error: NullPointerException
2025-07-24 00:45:26.679 [StudyCards-Async-2] ERROR PERFORMANCE - 
                [trackSearch] [0ms] [] FAILED_SERVICE_METHOD: UserService.getCurrentUser failed after 0ms with error: NullPointerException
2025-07-24 00:46:12.350 [http-nio-8082-exec-7] INFO  PERFORMANCE - 
                [getComprehensiveStatistics] [565ms] [] SERVICE_METHOD: StudySessionService.getComprehensiveStatistics took 565ms
