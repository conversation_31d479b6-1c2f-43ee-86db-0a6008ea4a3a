2025-07-24 00:09:13.659 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT secret validation successful. Secret length: 87 bytes
2025-07-24 00:09:13.667 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT configuration initialized successfully
2025-07-24 00:09:13.667 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT expiration: 14400000 ms (4 hours)
2025-07-24 00:09:13.667 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT refresh expiration: 604800000 ms (168 hours)
2025-07-24 00:09:13.667 [main] INFO  com.studycards.security.JwtUtils - 
                [] [] [] [] JWT configuration: JwtProperties{expiration=14400000, refreshExpiration=604800000, refreshCookieName='studycards-refresh-token', issuer='StudyCards', audience='StudyCards-Users', maxSessionDurationDays=365, refreshWindowDays=7, requireHttps=false, includeDetailedErrors=true, secretLength=116}
