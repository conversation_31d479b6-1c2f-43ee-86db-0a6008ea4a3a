// src/hooks/useSearchOperations.js
import { useCallback } from 'react';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { useSearch } from '../contexts/SearchContext';
import { searchAPI } from '../api/search';
import { toast } from 'react-hot-toast';

/**
 * Custom hook for search operations with centralized state management
 */
export const useSearchOperations = () => {
  const { state, actions } = useSearch();
  const queryClient = useQueryClient();

  // Deck search query
  const deckSearchQuery = useQuery(
    ['unifiedDeckSearch', state.query, state.filters.decks, state.pagination.decks.page],
    async () => {
      if (!state.query && !hasActiveFilters('decks')) {
        return null;
      }

      const searchRequest = {
        query: state.query || null,
        ...state.filters.decks,
        page: state.pagination.decks.page,
        size: state.pagination.decks.size,
        searchContext: state.searchContext
      };

      const response = await searchAPI.unifiedSearchDecks(searchRequest);
      
      // Update pagination
      actions.setPagination({
        page: response.number,
        size: response.size,
        totalElements: response.totalElements,
        totalPages: response.totalPages
      }, 'decks');

      // Add to search history
      if (state.query) {
        actions.addToHistory({
          query: state.query,
          searchType: 'decks',
          filters: state.filters.decks,
          resultCount: response.totalElements
        });
      }

      return response;
    },
    {
      enabled: state.searchType === 'decks',
      keepPreviousData: true,
      staleTime: 30000, // 30 seconds
      onSuccess: (data) => {
        if (data) {
          actions.setResults(data, 'decks');
        }
      },
      onError: (error) => {
        console.error('Deck search error:', error);
        actions.setError(`Failed to search decks: ${error.message}`);
        toast.error('Failed to search decks');
      }
    }
  );

  // Card search query
  const cardSearchQuery = useQuery(
    ['unifiedCardSearch', state.query, state.filters.cards, state.pagination.cards.page],
    async () => {
      if (!state.query && !hasActiveFilters('cards')) {
        return null;
      }

      const searchRequest = {
        query: state.query || null,
        ...state.filters.cards,
        page: state.pagination.cards.page,
        size: state.pagination.cards.size
      };

      const response = await searchAPI.unifiedSearchCards(searchRequest);
      
      // Update pagination
      actions.setPagination({
        page: response.number,
        size: response.size,
        totalElements: response.totalElements,
        totalPages: response.totalPages
      }, 'cards');

      // Add to search history
      if (state.query) {
        actions.addToHistory({
          query: state.query,
          searchType: 'cards',
          filters: state.filters.cards,
          resultCount: response.totalElements
        });
      }

      return response;
    },
    {
      enabled: state.searchType === 'cards',
      keepPreviousData: true,
      staleTime: 30000,
      onSuccess: (data) => {
        if (data) {
          actions.setResults(data, 'cards');
        }
      },
      onError: (error) => {
        console.error('Card search error:', error);
        actions.setError(`Failed to search cards: ${error.message}`);
        toast.error('Failed to search cards');
      }
    }
  );

  // Quick search query
  const quickSearchQuery = useQuery(
    ['quickSearch', state.query],
    () => searchAPI.quickSearch(state.query, 10),
    {
      enabled: false, // Only run when explicitly called
      staleTime: 60000, // 1 minute
      onError: (error) => {
        console.error('Quick search error:', error);
        toast.error('Quick search failed');
      }
    }
  );

  // Search suggestions query
  const suggestionsQuery = useQuery(
    ['searchSuggestions', state.query, state.searchType],
    () => searchAPI.getSearchSuggestions(state.query, state.searchType, 10),
    {
      enabled: Boolean(state.query && state.query.length >= 2),
      staleTime: 300000, // 5 minutes
      onSuccess: (data) => {
        actions.setSuggestions(data);
      },
      onError: (error) => {
        console.error('Search suggestions error:', error);
      }
    }
  );

  // Search mutation for immediate searches
  const searchMutation = useMutation(
    async ({ query, searchType, filters }) => {
      actions.setLoading(true);
      actions.setQuery(query);
      
      if (searchType) {
        actions.setSearchType(searchType);
      }
      
      if (filters) {
        actions.setFilters(filters, searchType || state.searchType);
      }

      // Update URL
      actions.updateUrl(query, searchType || state.searchType);

      // Invalidate and refetch queries
      await queryClient.invalidateQueries(['unifiedDeckSearch']);
      await queryClient.invalidateQueries(['unifiedCardSearch']);
    },
    {
      onError: (error) => {
        console.error('Search mutation error:', error);
        actions.setError(error.message);
        actions.setLoading(false);
      }
    }
  );

  // Helper functions
  const hasActiveFilters = useCallback((searchType) => {
    const filters = state.filters[searchType];

    // Guard against undefined/null filters to prevent Object.values error
    if (!filters || typeof filters !== 'object') {
      return false;
    }

    return Object.values(filters).some(value => {
      if (Array.isArray(value)) return value.length > 0;
      if (typeof value === 'boolean') return value === true;
      return value !== null && value !== undefined && value !== '';
    });
  }, [state.filters]);

  const clearFilters = useCallback((searchType) => {
    // Validate searchType to prevent errors
    if (!searchType || (searchType !== 'decks' && searchType !== 'cards')) {
      console.warn('Invalid searchType provided to clearFilters:', searchType);
      return;
    }

    const defaultFilters = searchType === 'decks' ? {
      isPublic: null,
      creatorId: null,
      tagNames: [],
      minDifficulty: null,
      maxDifficulty: null,
      minCardCount: null,
      maxCardCount: null,
      includeFolders: null,
      parentFolderId: null,
      isCollaborative: null,
      createdAfter: null,
      createdBefore: null,
      updatedAfter: null,
      updatedBefore: null,
      favoritesOnly: false,
      includeExpiredCreators: false,
      enableRelevanceRanking: true,
      rankingStrategy: 'relevance'
    } : {
      deckIds: [],
      tagNames: [],
      minDifficulty: null,
      maxDifficulty: null,
      minProgress: null,
      maxProgress: null,
      createdAfter: null,
      createdBefore: null,
      updatedAfter: null,
      updatedBefore: null,
      reviewDateAfter: null,
      reviewDateBefore: null,
      includeCollaborative: true,
      includePublicDecks: true,
      includePrivateDecks: true,
      includeDueCards: false
    };

    actions.setFilters(defaultFilters, searchType);
  }, [actions]);

  const executeSearch = useCallback((query, searchType, filters) => {
    searchMutation.mutate({ query, searchType, filters });
  }, [searchMutation]);

  const executeQuickSearch = useCallback((query) => {
    actions.setQuery(query);
    return quickSearchQuery.refetch();
  }, [actions, quickSearchQuery]);

  const changePage = useCallback((page, searchType) => {
    const targetSearchType = searchType || state.searchType;

    // Validate searchType to prevent errors
    if (!targetSearchType || (targetSearchType !== 'decks' && targetSearchType !== 'cards')) {
      console.warn('Invalid searchType provided to changePage:', targetSearchType);
      return;
    }

    actions.setPagination({ page }, targetSearchType);
  }, [actions, state.searchType]);

  const changePageSize = useCallback((size, searchType) => {
    const targetSearchType = searchType || state.searchType;

    // Validate searchType to prevent errors
    if (!targetSearchType || (targetSearchType !== 'decks' && targetSearchType !== 'cards')) {
      console.warn('Invalid searchType provided to changePageSize:', targetSearchType);
      return;
    }

    actions.setPagination({ size, page: 0 }, targetSearchType);
  }, [actions, state.searchType]);

  return {
    // State
    state,
    actions,
    
    // Queries
    deckSearchQuery,
    cardSearchQuery,
    quickSearchQuery,
    suggestionsQuery,
    
    // Mutations
    searchMutation,
    
    // Helper functions
    hasActiveFilters,
    clearFilters,
    executeSearch,
    executeQuickSearch,
    changePage,
    changePageSize,
    
    // Computed values
    isLoading: deckSearchQuery.isLoading || cardSearchQuery.isLoading || searchMutation.isLoading,
    hasResults: state.results.decks?.content?.length > 0 || state.results.cards?.content?.length > 0,
    totalResults: (state.results.decks?.totalElements || 0) + (state.results.cards?.totalElements || 0)
  };
};
